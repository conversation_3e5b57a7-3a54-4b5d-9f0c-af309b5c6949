const accountId="**********************************";
const authToken="56739814f7595abc7354818a8355dafe"
const testNum=+***********
import twilio from "twilio";

const smsTwilio=twilio(accountId,authToken)
const sendSms=async(number,text)=>{
    
    console.log('number 1: ', number);
    try {
        await smsTwilio.messages.create({
           body:text,
           from:testNum,
           to:number
        })
        console.log("Send Sms Success");
    } catch (error) {
        console.log(error,"sms not sent");
    }
}
export default sendSms;