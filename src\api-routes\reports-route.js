import express  from "express";
const routes=express.Router();

import {
    getItemsSalesLastMonth,
    getItemsSalesByProduct,
    getAllStripeTransactions,
    
} from "../api/reports.js"
import {getTransactionReportsLastMonth} from "../api/transaction-reports.js"
import {getMonthlyDiscountReport} from "../api/discountReport.js"
import {getDailyShiftReport} from "../api/shift-reports.js"
import {getTipweeklyDReport} from "../api/tip-reports.js"


routes.get('/Z-report', getItemsSalesLastMonth);
routes.get('/transaction-report',getTransactionReportsLastMonth);
routes.get('/shift-report',getDailyShiftReport);
routes.get('/discount-report',getMonthlyDiscountReport);
routes.get('/tip-report',getTipweeklyDReport);
routes.get('/getItemsSalesReport', getItemsSalesByProduct);
// GET /api/stripe-report/:accountId?startDate=YYYY-MM-DD&endDate=YYYY-MM-DD
routes.get('/stripe-report/:accountId', getAllStripeTransactions);
export default routes