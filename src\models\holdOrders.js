import mongoose from "mongoose";

const holdOrdersSchema = new mongoose.Schema({
  orderSummury: {
    type: Object,
  },
  userId: { type: mongoose.Schema.Types.ObjectId, required: true, ref: "user" },
  employee: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Employee",
  },
  table: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "tables",
    },
  ],
});

const holdOrder = mongoose.model("holdOrder", holdOrdersSchema);
export default holdOrder;
