import mongoose from 'mongoose'
const tableReservationAndWaitingListSchema=new mongoose.Schema({
    fullName:{
        type:String
    },
    address:{
        type:String,
    },
    email:{
        type:String
    },
    contactNumber:{
        type:String
    },
    reserveDate:{
        type:String
    },
    reserveTime:{
        type:String
    },
    visitorNote:{
        type:String
    },
    tags:{
        type:String,
        enum:['VIP','Birthday','Aniversary','Pivate Dining']
    },
    takenBy:{
        type:String
    },
    pagerNumber:{
        type:Number
    },
    table:{
        type:mongoose.Schema.Types.ObjectId,
        ref:'tables'
    },
    partySize:{
        type:Number 
    },
    site:{
        type:mongoose.Schema.Types.ObjectId,
        ref:'sitesiteManagment'
    },
    userId:{
        type:mongoose.Schema.Types.ObjectId,
        required:true,
        ref:'user'
    }
},
{timestamps:true});
const tableSelect=mongoose.model("tableRservationAndWaitingList",tableReservationAndWaitingListSchema);
export default tableSelect;