import { employee, employeeType, Deduction } from '../models/employee.js';


export const getEmployeeType = async (req, res) => {
    let filter = {}
    if (req.query.userId) {
        filter = { userId: req.query.userId.split(',') }
    }
    if (req.query.role) {
        filter = { role: req.query.role.split(',') }
    }

    let data = await employeeType.find(filter).populate("userId")
    res.send(data);

}


// export const getEmployee = async (req, res) => {
//     let filter = {}
//     if (req.query.userId) {
//         filter = { userId: req.query.userId.split(',') }
//     }
//     if (req.query.role) {
//         filter = { role: req.query.role.split(',') }
//     }
//     if (req.query.firstName) {
//         filter = { firstName: req.query.firstName.split(',') }
//     }
//     let data = await employee.find(filter).populate("employeeType")
//     res.send(data);

// }
export const getEmployee = async (req, res) => {
    try {
        const filter = {
            $or: [{ IsDeleted: false }, { IsDeleted: { $exists: false } }],
            ...(req.query.userId && { userId: { $in: req.query.userId.split(',') } }),
            ...(req.query.role && { role: { $in: req.query.role.split(',') } }),
            ...(req.query.firstName && { firstName: { $in: req.query.firstName.split(',') } })
        };

        const data = await employee.find(filter).populate("employeeType");
        res.send(data);
    } catch (error) {
        res.status(500).send({ message: "Error fetching employees", error });
    }
};

// export const getEmployeeById = async (req, res) => {
//     let data = await employee.findOne(req.params).populate("employeeType")
//     res.send(data);

// }
export const getEmployeeById = async (req, res) => {
    try {
        const filter = {
            ...req.params,
            $or: [{ IsDeleted: false }, { IsDeleted: { $exists: false } }]
        };

        const data = await employee.findOne(filter).populate("employeeType");
        if (!data) return res.status(404).send({ message: "Employee not found" });
        res.send(data);
    } catch (error) {
        res.status(500).send({ message: "Error fetching employee by ID", error });
    }
};


export const getEmployeeTypeById = async (req, res) => {
    let data = await employeeType.findOne(req.params)
    res.send(data);
}
export const postEmployeeType = async (req, res) => {
    const { userId, name } = req.body;
    const data = await new employeeType({ userId, name });
    await data.save().then(result => {
        console.log(result, "Employee data save to database")
        res.json({
            name: result.name,
            userId: result.userId
        })
    }).catch(err => {
        res.status(400).send('unable to save database');
        console.log(err)
    })
}
// export const postEmployee = async (req, res) => {
//     const { userName, firstName, lastName, email, password,confirmPassword, employeeId, userId,role,hourlyRate,employeeType, overTimeRate,totalHours, phoneNo,address  } = req.body;
//     const data = await new employee({ userName, firstName, lastName,confirmPassword, email, password,employeeId, userId,role,hourlyRate,employeeType, overTimeRate, totalHours, phoneNo, address});
//     await data.save().then(result => {
//         console.log(result, "Employee data save to database")
//         res.json({
//             userName: result.userName,
//             firstName: result.firstName,
//             lastName: result.lastName,
//             email: result.email,
//             employeeId: result.employeeId,
//             password: result.password,
//             userId:result.userId,
//             confirmPassword:result.confirmPassword,
//             role:result.role,
//             hourlyRate:result.hourlyRate,
//             employeeType: result.employeeType,
//             totalHours: result.totalHours,
//             overTimeRate: result.overTimeRate,
//             address: result.address,
//             phoneNo: result.phoneNo,
//         })
//     }).catch(err => {
//         res.status(400).send('unable to save database');
//         console.log(err)
//     })
// }



export const postEmployee = async (req, res) => {
    try {
        const {
            userName,
            firstName,
            lastName,
            email,
            password,
            confirmPassword,
            employeeId,
            userId,
            role,
            hourlyRate,
            employeeType,
            overTimeRate,
            totalHours,
            phoneNo,
            address,
            deductions,
            employeeStartTime,
            employeeEndTime
        } = req.body;

        const newEmployee = new employee({
            userName,
            firstName,
            lastName,
            email,
            password,
            confirmPassword,
            employeeId,
            userId,
            role,
            hourlyRate,
            employeeType,
            overTimeRate,
            totalHours,
            phoneNo,
            address,
            employeeStartTime,
            employeeEndTime,
            deductions: deductions || [],
        });

        // Save the new employee
        const savedEmployee = await newEmployee.save();

        res.json(savedEmployee);
    } catch (error) {
        console.error(error);
        res.status(400).send('Unable to save to the database.');
    }
};
export const employeeLogin = async (req, res) => {
    const { employeeId } = req.body;
    const employe = await employee.findOne({ employeeId });
    if (!employe) {
        return res.status(400).send({ message: "Employee doesn't exist" });
    }
    // Get current time in 24-hour format
    const now = new Date();
    const currentHours = now.getHours();
    const currentMinutes = now.getMinutes();
    // Function to convert "HH:MM" 24-hour time to minutes for comparison
    const timeToMinutes = (time) => {
        if (!time) return null; // Handle undefined values
        const [hours, minutes] = time.split(':').map(Number);
        return hours * 60 + minutes; // Convert to total minutes
    };
    // Convert employee's login period to minutes
    const employeStartTimeMinutes = timeToMinutes(employe?.employeeStartTime);
    const employeEndTimeMinutes = timeToMinutes(employe?.employeeEndTime);
    // Convert current time to minutes
    const currentTimeMinutes = currentHours * 60 + currentMinutes;
    // Check if login is within the allowed period
    if (
        employeStartTimeMinutes !== null && employeEndTimeMinutes !== null &&
        (currentTimeMinutes < employeStartTimeMinutes || currentTimeMinutes > employeEndTimeMinutes)
    ) {
        return res.status(403).json({
            message: `Access Denied: This is not the designated employee login period. Employees can only log in between ${employe.employeeStartTime} and ${employe.employeeEndTime}.`
        });
    }

    // Login successful
    res.status(200).json({
        message: "Employee Login Successfully",
        userId: employe.userId,
        startDate: employe.startDate,
        employeId: employe._id,
        firstName: employe.firstName,
        lastName: employe.lastName,
        employeeId: employe.employeeId,
        hourlyRate: employe.hourlyRate,
        employeeType: employe.employeeType,
        phoneNo: employe.phoneNo,
        address: employe.address
    });
};


// export const employeeLogin = async (req, res) => {
//     const { employeeId } = req.body
//     const employe = await employee.findOne({ employeeId });
//     if (!employe) {
//         return res.status(400).send({ message: "employee does'nt Exists" });
//     }
//    // Calculate the current time
//    const now = new Date();
//    let hours = now.getHours();
//    const minutes = now.getMinutes();
//    const seconds = now.getSeconds()
//    let timeFormate = hours >= 12 ? 'PM' : 'AM';
//    hours = hours % 12;
//    hours = hours ? hours : 12;
//    const loginTime = hours + ":" + (minutes < 10 ? "0" + minutes : minutes) + ":" + (seconds < 10 ? "0" + seconds : seconds) + " " + timeFormate;
//     // Retrieve employee's designated login start and end times from database
//     const convertTo12HourFormat = (time24) => {
//         console.log("time24", time24)
//         if (!time24 && time24 !== 0) {
//             return time24 // Return 'undefined' if the time is not defined
//         }else{
//  // Split the time into hours and minutes
//  const [hours, minutes] = time24?.split(':');

//  // Convert hours to 12-hour format
//  let timeFormat = 'AM';
//  let formattedHours = parseInt(hours, 10);
//  if (formattedHours >= 12) {
//      timeFormat = 'PM';
//      if (formattedHours > 12) {
//          formattedHours -= 12;
//      }
//  }

//  // Format hours, minutes, and add AM/PM suffix
//  const formattedTime = `${formattedHours}:${minutes} ${timeFormat}`;
//  return formattedTime;
//         }

//     };

//     // Convert start and end times to 12-hour format
//     const employeStartTime = convertTo12HourFormat(employe?.employeeStartTime);
//     const employeEndTime = convertTo12HourFormat(employe?.employeeEndTime);


//     if (employe.employeeId != employeeId) {
//         return res.status(400).send({ message: "wrong employeeId" });
//     } else if (employe.employeeId == employeeId && loginTime >= employeStartTime && loginTime <= employeEndTime || !employeStartTime || !employeEndTime ) {
//         res.status(200).json({ message: "Employee Login Successfully", userId: employe.userId, startDate: employe.startDate, employeId: employe._id, firstName: employe.firstName, lastName: employe.lastName, employeeId: employe.employeeId, hourlyRate: employe.hourlyRate, employeeType: employe.employeeType, phoneNo: employe.phoneNo, address: employe.address });
//     }else{

//         const errorMessage = `Access Denied: This is not the designated employee login period. Employees can only log in between ${employeStartTime} and ${employeEndTime}.`;
//         return res.status(403).json({ message: errorMessage });
//     }

// }

// export const updateEmployee = async (req, res) => {
//     console.log(req.params);
//     let data = await employee.findByIdAndUpdate(
//         { _id: req.params._id }, {
//         $set: req.body
//     },
//         { new: true }
//     )
//     if (data) {
//         res.send({ message: "employee data updated successfully" });
//     }
//     else {
//         res.send({ message: "employee data cannot be updated successfully" })
//     }
// }

// export const employeeLogin = async (req, res) => {
//     try {
//         // Fetch employee details from the database
//         const employe = await employee.findOne({ employeeId });

//         if (!employe) {
//             return res.status(400).json({ message: "Employee doesn't exist" });
//         }

//         // Function to convert 24-hour time (HH:mm) to timestamp
//         const convertToTimestamp = (time24) => {
//             if (!time24) return null;
//             const [hours, minutes] = time24.split(':').map(Number);
//             const now = new Date();
//             return new Date(now.getFullYear(), now.getMonth(), now.getDate(), hours, minutes, 0).getTime();
//         };

//         // Get current login time in milliseconds
//         const loginTimestamp = new Date().getTime();

//         // Convert stored start and end times to timestamps
//         const employeStartTime = convertToTimestamp(employe?.employeeStartTime);
//         const employeEndTime = convertToTimestamp(employe?.employeeEndTime);

//         console.log("Employee ID:", employeeId);
//         console.log("Stored Employee ID:", employe.employeeId);
//         console.log("Login Timestamp:", new Date(loginTimestamp));
//         console.log("Start Timestamp:", employeStartTime ? new Date(employeStartTime) : "N/A");
//         console.log("End Timestamp:", employeEndTime ? new Date(employeEndTime) : "N/A");

//         // Check if employeeId is correct
//         if (employe.employeeId != employeeId) {
//             return res.status(400).json({ message: "Wrong employee ID" });
//         }

//         // Allow login if within designated hours or if no time restrictions exist
//         if (
//             (employeStartTime && employeEndTime && loginTimestamp >= employeStartTime && loginTimestamp <= employeEndTime) ||
//             !employeStartTime || !employeEndTime
//         ) {
//             return res.status(200).json({
//                 message: "Employee Login Successfully",
//                 userId: employe.userId,
//                 startDate: employe.startDate,
//                 employeId: employe._id,
//                 firstName: employe.firstName,
//                 lastName: employe.lastName,
//                 employeeId: employe.employeeId,
//                 hourlyRate: employe.hourlyRate,
//                 employeeType: employe.employeeType,
//                 phoneNo: employe.phoneNo,
//                 address: employe.address
//             });
//         } else {
//             return res.status(403).json({
//                 message: `Access Denied: Employees can only log in between ${employe.employeeStartTime} and ${employe.employeeEndTime}.`
//             });
//         }
//     } catch (error) {
//         console.error("Error in Employee Login:", error);
//         return res.status(500).json({ message: "Internal Server Error" });
//     }
// };


export const updateEmployee = async (req, res) => {
    try {
        const updatedEmployee = await employee.findByIdAndUpdate(
            { _id: req.params._id },
            {
                $set: {
                    ...req.body,
                    deductions: req.body.deductions || [],  // Default to an empty array if no deductions provided
                },
            },
            { new: true }
        );

        if (updatedEmployee) {
            res.json({ updatedEmployee, message: "Employee data updated successfully", updatedEmployee });
        } else {
            res.status(404).json({ message: "Employee data not found" });
        }

    } catch (error) {
        console.error(error);
        res.status(400).send('Unable to update employee data.');
    }
}
// export const deleteEmployee = async (req, res) => {
//     console.log(req.params)
//     let data = await employee.deleteOne(req.params)
//     if (data) {
//         res.send({ message: "employee data delete successfully" });
//     }
//     else {
//         res.send({ message: "employee data cannot delete successfully" })
//     }
// }

export const deleteEmployee = async (req, res) => {
    try {
        const { _id } = req.params;

        const result = await employee.updateOne(
            { _id },
            { $set: { IsDeleted: true } }
        );

        if (result.modifiedCount > 0) {
            res.send({ message: "Employee deleted successfully" });
        } else {
            res.status(404).send({ message: "Employee not found or already deleted" });
        }
    } catch (error) {
        console.error("Error deleting employee:", error);
        res.status(500).send({ message: "Internal Server Error" });
    }
};

export const deleteEmployeeType = async (req, res) => {
    console.log(req.params)
    let data = await employeeType.deleteOne(req.params)
    if (data) {
        res.send({ message: "employee data delete successfully" });
    }
    else {
        res.send({ message: "employee data cannot delete successfully" })
    }
}


// Create a new deduction
export const createDeduction = async (req, res) => {
    try {
        const newDeduction = new Deduction(req.body);
        const savedDeduction = await newDeduction.save();
        res.json(savedDeduction);
    } catch (error) {
        console.error(error);
        res.status(400).send('Unable to create deduction.');
    }
};

// Get all deductions
export const getAllDeductions = async (req, res) => {
    try {
        const deductions = await Deduction.find();
        res.json(deductions);
    } catch (error) {
        console.error(error);
        res.status(500).send('Internal Server Error');
    }
};

// Get a specific deduction by ID
export const getDeductionById = async (req, res) => {
    try {
        const deduction = await Deduction.findById(req.params.id);
        if (deduction) {
            res.json(deduction);
        } else {
            res.status(404).json({ message: "Deduction not found" });
        }
    } catch (error) {
        console.error(error);
        res.status(500).send('Internal Server Error');
    }
};

// Update a deduction by ID
export const updateDeductionById = async (req, res) => {
    try {
        const updatedDeduction = await Deduction.findByIdAndUpdate(
            req.params.id,
            { $set: req.body },
            { new: true }
        );

        if (updatedDeduction) {
            res.json(updatedDeduction);
        } else {
            res.status(404).json({ message: "Deduction not found" });
        }
    } catch (error) {
        console.error(error);
        res.status(500).send('Internal Server Error');
    }
};

// Delete a deduction by ID
export const deleteDeductionById = async (req, res) => {
    try {
        const deletedDeduction = await Deduction.findByIdAndDelete(req.params.id);

        if (deletedDeduction) {
            res.json({ message: "Deduction deleted successfully", deletedDeduction });
        } else {
            res.status(404).json({ message: "Deduction not found" });
        }
    } catch (error) {
        console.error(error);
        res.status(500).send('Internal Server Error');
    }
};
