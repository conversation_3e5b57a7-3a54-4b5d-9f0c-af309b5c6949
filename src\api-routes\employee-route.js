import express  from "express";
const routes=express.Router();

import {getEmployee,
    postEmployee,
    updateEmployee,
    deleteEmployee,
    employeeLogin,getEmployeeById,
    getEmployeeType,
    postEmployeeType,
    deleteEmployeeType,
    getEmployeeTypeById,
    createDeduction,
    getAllDeductions,
    getDeductionById,
    updateDeductionById,
    deleteDeductionById,
} from "../api/employee.js"

routes.get('/employee', getEmployee )
routes.get('/employeetype', getEmployeeType )
routes.get('/employeetype/:_id', getEmployeeTypeById )
routes.get('/employee/:_id', getEmployeeById )

routes.post('/employee', postEmployee )
routes.post('/employeetype', postEmployeeType )
routes.post('/employeeLogin', employeeLogin )
routes.put('/employee/:_id', updateEmployee )
routes.delete('/employee/:_id', deleteEmployee )
routes.delete('/employeetype/:_id', deleteEmployeeType )

// Routes for deductions
routes.post('/deductions', createDeduction);
routes.get('/deductions', getAllDeductions);
routes.get('/deductions/:id', getDeductionById);
routes.put('/deductions/:id', updateDeductionById);
routes.delete('/deductions/:id', deleteDeductionById);

export default routes