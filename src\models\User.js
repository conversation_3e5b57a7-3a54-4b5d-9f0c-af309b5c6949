import mongoose from 'mongoose'
import Jo<PERSON> from 'joi';

var current = new Date();
const timeStamp = new Date(Date.UTC(current.getFullYear(),
  current.getMonth(), current.getDate(), current.getHours(),
  current.getMinutes(), current.getSeconds(), current.getMilliseconds()));
const Schema = mongoose.Schema
const UserSchema = new Schema({
  firstname: {
    type: String,
  },
  lastname: {
    type: String,
  },
  name:{
    type:String
  },
  email: {
    type: String,
    required: true,
    lowercase: true,
    unique: true
  },
  password: {
    type: String,
    // required: true,
  },
  role: {
    type: String,
  },
  phone: {
    type: String,
    require: true,
  },
  businessName: {
    type: String,
    require: true,
  },
  isActive: {
    type: Boolean,
    default: true
  },
  userId: {
    type: String
  },
  storeName: {
    type: String
  },
  stripe_acess_token: {
    type: String
  },
  stripe_account_id: {
    type: String
  },
  stripe_refresh_token: {
    type: String
  },
  appFee: {
    type: Number,
    default: 0
  },
  google: {
    type: Boolean,
    default: false
  },
  googleId: {
    type: String
  },
  githubRepo: {
    type: String
  },
  netlifyUrl: {
    type: String
  },
  hiddenPlan: {
    type: Boolean
  },
  surChargeThreshold:{
    type:Number
  },
  monthlySurchargePayments: [
    {
      month: Number,
      year: Number,
      paid: Boolean,
      amount: Number
    }
  ],
  createdDate: {
    type: Date,
    default: timeStamp
  }

}, { timestamps: true })

const superUserSchema = new Schema({
  firstname: {
    type: String,
  },
  lastname: {
    type: String,
  },
  email: {
    type: String,
    required: true,
    lowercase: true,
    unique: true
  },
  password: {
    type: String,
    required: true,
  },
  role: {
    type: String,
  },
  userId: {
    type: String
  }
})

// UserSchema.pre('save', async function (next) {
//   try {

//     if (this.isNew) {
//       const salt = await bcrypt.genSalt(10)
//       const hashedPassword = await bcrypt.hash(this.password, salt)
//       this.password = hashedPassword
//     }
//     next()
//   } catch (error) {
//     next(error)
//   }
// })

// UserSchema.methods.isValidPassword = async function (password) {
//   try {
//     return await bcrypt.compare(password, this.password)
//   } catch (error) {
//     throw error
//   }
// }
export const validate = (user) => {
  const schema = Joi.object({
    name: Joi.string().required(),
    email: Joi.string().email().required(),
    password: Joi.string().required(),
  });
  return schema.validate(user);
};
UserSchema.pre('remove', async function (next) {
  try {
    // Find all schemas with a reference to the User model
    const schemasWithUserReference = mongoose.modelNames().filter((modelName) => {
      console.log('modelName1: ', modelName);
      const schema = mongoose.model(modelName).schema;
      console.log('schema: ', schema);
      return schema && Object.keys(schema.tree).some(field => {
        const type = schema.tree[field].type;
        return type && type.ref === 'user' && field === 'userId';
      });
    });

    console.log('schemasWithUserReference:', schemasWithUserReference);

    // Delete related data from each schema
    const deletePromises = schemasWithUserReference.map(async (modelName) => {
      console.log('modelName2: ', modelName);
      const Model = mongoose.model(modelName);
      await Model.deleteMany({ userId: this.userId });
    });

    await Promise.all(deletePromises);

    next();
  } catch (error) {
    next(error);
  }
});



export const User = mongoose.model('user', UserSchema)
export const superUser = mongoose.model('superUser', superUserSchema)

// export {User,superUser};
