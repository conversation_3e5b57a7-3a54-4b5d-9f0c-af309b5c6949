import PlaylandOrder from '../models/playlandOrder.js';

export const createOrder = async (req, res) => {
    const order = new PlaylandOrder(req.body);
    try {
        const savedOrder = await order.save();
        res.status(201).json(savedOrder);
    } catch (err) {
        res.status(400).json({ message: err.message });
    }
};

export const getOrdersByUserId = async (req, res) => {
    try {
        console.log("req.params.userId  ; ", req.params.userId)
        // Find orders by userId
        const orders = await PlaylandOrder.find({ userId: req.params.userId });
        console.log("orders ; ",orders)
        // Check if any orders are found
        if (!orders.length) {
            return res.status(404).json({ message: 'No orders found for this user' });
        }
        
        // Respond with the orders
        res.json(orders);
    } catch (err) {
        // Handle any errors
        res.status(500).json({ message: err.message });
    }
};

export const getOrders = async (req, res) => {
    try {
        const orders = await PlaylandOrder.find();
        res.json(orders);
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
};

export const getOrderById = async (req, res) => {
    try {
        const order = await PlaylandOrder.findById(req.params.id);
        if (!order) {
            return res.status(404).json({ message: 'Cannot find order' });
        }
        res.json(order);
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
};

export const updateOrder = async (req, res) => {
    try {
        const order = await PlaylandOrder.findById(req.params.id);
        if (!order) {
            return res.status(404).json({ message: 'Cannot find order' });
        }
        Object.assign(order, req.body);
        const updatedOrder = await order.save();
        res.json(updatedOrder);
    } catch (err) {
        res.status(400).json({ message: err.message });
    }
};

export const deleteOrder = async (req, res) => {
    try {
        const order = await PlaylandOrder.findById(req.params.id);
        if (!order) {
            return res.status(404).json({ message: 'Cannot find order' });
        }
        await order.remove();
        res.json({ message: 'Deleted order' });
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
};
