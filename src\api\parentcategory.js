import parentcategory from '../models/parentcategory.js';

export const getParentCategories = async (req, res) => {
    try {
        let filter = {};

        if (req.query.userId) {
            filter.userId = { $in: req.query.userId.split(',') };
        }

        // Fetch necessary fields only and optimize populate
        const data = await parentcategory
            .find(filter, { _id: 1, name: 1, userId: 1,parent_pic:1  }) // Fetch limited fields
            .populate('userId', '_id') // Optimize population
            .lean() // Convert to plain JS objects
            .exec(); // Explicit execution for better performance

        res.json(data);
    } catch (error) {
        res.status(500).json({ message: 'Server error', error });
    }
};


export const getParentCategoriesById = async (req, res) => {
   
    let data = await parentcategory.findOne(req.params);
    res.send(data)
}
export const postParentCategories = async (req, res) => {
    const { name,userId } = req.body;
    const parent_pic=req.file ? req.file.location : req.body.parent_pic || null
    let data = await new parentcategory({ name,userId ,parent_pic});
    await data.save().then(result => {
        console.log("Category data saved to database");
        res.json({
            _id: result._id,
            name: result.name,
            userId:result.userId,
            parent_pic:result.parent_pic
        })
    }).catch(err => {
        res.status(400).send("unable to save to database");
        console.log(err)
    });
}
export const updateParentCategories = async (req, res) => {
    let parent_pic
    if(req.file){
     parent_pic=req.file ? req.file.location : req.body.parent_pic || null
    }
    console.log(req.params)
    let data = await parentcategory.findByIdAndUpdate(
        { _id: req.params._id }, {
        $set: req.body,parent_pic:parent_pic
    },
        { new: true }
    );
    if (data) {
        res.send({data, message: "category data updated successfully" });
    } else {
        res.send({ message: "category data cannot be updated successfully" })
    }
}
export const deleteParentCategories = async (req, res) => {
    console.log(req.params)
    let data = await parentcategory.deleteOne(req.params)
    if (data) {
        res.send({ message: "category data delete successfully" });
    } else {
        res.send({ message: "category data cannot delete successfully" })
    }
}
