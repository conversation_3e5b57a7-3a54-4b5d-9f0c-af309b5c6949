import express  from "express";
const routes=express.Router();

import {postEmailMarketing,
    getemailMarketing,
    Mailchimp,
    deleteEmailMarketing
} from "../api/email-Marketing.js"

routes.get('/emailMarketing', getemailMarketing )
routes.post('/emailMarketing', postEmailMarketing )
routes.delete('/emailMarketing/:id', deleteEmailMarketing )
routes.post('/Mailchimp', Mailchimp )
export default routes