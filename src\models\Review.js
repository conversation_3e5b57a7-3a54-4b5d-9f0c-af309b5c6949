import mongoose from 'mongoose';

const ReviewSchema = new mongoose.Schema({
  customerId:{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'customer'
},
rating:{
    type: Number,
},
review:{
    type:String
},
productId:{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'product'
},
Profession:{
    type:String
},
creatdateFormat: {
    type: Date,
    default: Date.now
},
likes:{
    type:Number
}

})


const reviewModel = mongoose.model('review', ReviewSchema)
export default reviewModel;