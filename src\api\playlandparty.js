import Party from '../models/playlandparty.js';  // Ensure the path to your model is correct




export const getParties = async (req, res) => {
    try {
        const parties = await Party.party.find();
        res.json(parties);
    } catch (err) {
        res.status(400).send("Unable to fetch parties from database");
        console.log(err);
    }
};

export const getPartyById = async (req, res) => {
    try {
        const party = await Party.party.findById(req.params.id);
        if (!party) {
            return res.status(404).send("Party not found");
        }
        res.json(party);
    } catch (err) {
        res.status(400).send("Unable to fetch party from database");
        console.log(err);
    }
};

export const postParty = async (req, res) => {
    const { partyname, shortDescription, longDescription, price, userId, active, orderCapacity, totalCapacity, ExtraCapPrice } = req.body;

    const party_pic = req.file ? req.file.location : req.body.party_pic || null;

    let parsedLongDescription;
    let totalCapacityNum;
    let priceNum;
    let extraCapPriceNum;

    try {
        parsedLongDescription = JSON.parse(longDescription);
        totalCapacityNum = Number(totalCapacity);
        priceNum = Number(price);
        extraCapPriceNum = Number(ExtraCapPrice);

        if (isNaN(totalCapacityNum) || isNaN(priceNum) || isNaN(extraCapPriceNum)) {
            throw new Error("Invalid number format");
        }
    } catch (err) {
        return res.status(400).send("Invalid input format");
    }

    let data = new Party.party({
        partyname,
        party_pic,
        shortDescription,
        longDescription: parsedLongDescription,
        price: priceNum,
        userId,
        active,
        orderCapacity,
        totalCapacity: totalCapacityNum,
        ExtraCapPrice: extraCapPriceNum
    });

    try {
        let result = await data.save();
        console.log("Party data saved to database");
        res.json({
            id: result.id,
            partyname: result.partyname,
            party_pic: result.party_pic,
            shortDescription: result.shortDescription,
            longDescription: result.longDescription,
            price: result.price,
            userId: result.userId,
            active: result.active,
            orderCapacity: result.orderCapacity,
            totalCapacity: result.totalCapacity,
            ExtraCapPrice: result.ExtraCapPrice
        });
    } catch (err) {
        res.status(400).send("Unable to save to database");
        console.log(err);
    }
};
export const updateParty = async (req, res) => {
    const { partyname, shortDescription, longDescription, price, userId, active, orderCapacity, totalCapacity, ExtraCapPrice } = req.body;
    let party_pic = req.file ? req.file.location : req.body.party_pic || null;

    let parsedActive, parsedLongDescription;
    let priceNum, totalCapacityNum, extraCapPriceNum;

    try {
        parsedActive = JSON.parse(active);
        parsedLongDescription = JSON.parse(longDescription);
        priceNum = Number(price);
        totalCapacityNum = Number(totalCapacity);
        extraCapPriceNum = Number(ExtraCapPrice);

        if (isNaN(priceNum) || isNaN(totalCapacityNum) || isNaN(extraCapPriceNum)) {
            throw new Error("Invalid number format");
        }
    } catch (err) {
        return res.status(400).send("Invalid input format");
    }

    try {
        const updatedParty = await Party.party.findByIdAndUpdate(
            req.params.id,
            {
                partyname,
                party_pic,
                shortDescription,
                longDescription: parsedLongDescription,
                price: priceNum,
                userId,
                active: parsedActive,
                orderCapacity,
                totalCapacity: totalCapacityNum,
                ExtraCapPrice: extraCapPriceNum
            },
            { new: true }
        );

        if (!updatedParty) {
            return res.status(404).send("Party not found");
        }

        res.json(updatedParty);
    } catch (err) {
        res.status(400).send("Unable to update party in database");
        console.log(err);
    }
};


export const deleteParty = async (req, res) => {
    try {
        const deletedParty = await Party.party.findByIdAndDelete(req.params.id);

        if (!deletedParty) {
            return res.status(404).send("Party not found");
        }

        res.json({ message: "Party successfully deleted" });
    } catch (err) {
        res.status(400).send("Unable to delete party from database");
        console.log(err);
    }
};
