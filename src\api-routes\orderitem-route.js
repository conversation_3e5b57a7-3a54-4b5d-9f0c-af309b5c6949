import express  from "express";
const routes=express.Router();

import {
    getOrderItemById,
    postOrderItem,
    updateOrderItem,
    updatedisplayOrders,
    deleteOrderItem,
    getOrderItemByUserId,
    getCustomersOrderItemByUserId,
    getOrderItemsWithRefundData,
    getOrderItemsLastWeek,
    getItemsSaleslastmonth,
    getOrderItemsByDateRange,
    getOrderItemslastmonth,
    getOrderItems,
    getLastOrderByUserId,
    getItemsSalesLastMonth,
    getModifierSalesReport,
    getOrderItemOrderStatus,
    getDueOrders,
    getLastWeekOrders,
    getPosKitchenOrders,
    getOrderItemsCurrentMonth

} from "../api/orderitem.js"

routes.get('/orderitem', getOrderItemByUserId )
routes.get('/customerOrderitem', getCustomersOrderItemByUserId )
routes.get('/orderitem/withRefundData', getOrderItemsWithRefundData)
routes.get('/orderitem/lastmonth', getOrderItemslastmonth)
routes.get('/Orderitem/lastWeek', getOrderItemsLastWeek)
routes.get('/orderitem/lastOrder', getLastOrderByUserId)
routes.get('/orderitems/date', getOrderItemsByDateRange)
routes.get(`/orderitems`, getOrderItemOrderStatus )
routes.get('/orderitem/:_id', getOrderItemById )
routes.get('/orderitem', getOrderItems ),
routes.get('/lastweek-orders', getLastWeekOrders)
routes.get('/pos-kitchen-orders', getPosKitchenOrders)
routes.get('/aggregated-orders', getItemsSaleslastmonth);
routes.get('/Modifier-Reports', getModifierSalesReport);
routes.get('/X-report', getItemsSalesLastMonth);
routes.get('/dueOrders', getDueOrders);
routes.get('/currentMonth-orders', getOrderItemsCurrentMonth);

routes.post('/orderitem', postOrderItem )
routes.put('/orderitems/:_id', updatedisplayOrders )
routes.put('/orderitem/:_id', updateOrderItem )
routes.delete('/orderitem/:_id', deleteOrderItem )


export default routes