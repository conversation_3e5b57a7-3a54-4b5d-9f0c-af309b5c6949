import tables from "../models/tables.js";

export const getTables = async (req, res) => {
  let filter = {};
  if (req.query.userId) {
    filter = { userId: req.query.userId.split(",") };
  }
  const data = await tables.find(filter).populate("location");
  res.send(data);
};
export const getTableById = async (req, res) => {
  const data = await tables.findOne(req.params).populate("location");
  res.send(data);
};

export const postTables = async (req, res) => {
  try {
    const {
      tableNo,
      tableimg,
      tableName,
      location,
      description,
      hasLampixDevice,
      userId,
      Status,
      height,
      width,
      x,
      y,
      capacity,
    } = req.body;
    // const tableimg = req.file ? req.file.location : null
    let data = new tables({
      tableNo,
      tableName,
      width,
      height,
      location,
      description,
      hasLampixDevice,
      userId,
      tableimg,
      x,
      y,
      Status,
      capacity,
    });
    const table = await data.save();
    const tableData = await tables.findById(table._id).populate("location");
    res.json(tableData);
  } catch (err) {
    console.error("Error saving table:", err);
    res.status(400).send("Unable to save to database");
  }
};
export const updateTables = async (req, res) => {
  console.log("req: ", req.body);
  // const tableimg = req.file ? req.file.location : req.body.tableimg
  // console.log('tableimg: ', tableimg);
  let data = await tables
    .findByIdAndUpdate(
      { _id: req.params._id },
      {
        $set: req.body,
      },
      { new: true }
    )
    .populate("location");
  if (data) {
    res.send({ data, message: "tables data updated successfully" });
  } else {
    res.send({ message: "tables data cannot be updated successfully" });
  }
};
//Search and update
export const SearchUpdateTables = async (req, res) => {
  // const tableimg = req.file ? req.file.location : null

  const { tableNo, tableName } = req.query;
  console.log(tableNo, tableName);
  let data = await tables.findOneAndUpdate(
    {
      $or: [
        { tableNo: { $eq: Number(tableNo) } },
        { tableName: { $regex: String(tableName) } },
      ],
    },
    {
      $set: req.body,
    },
    { new: true }
  );
  if (data) {
    res.json({ data, message: "posTable data update successfully" });
  } else {
    res.send({ message: "posTables data cannot update successfully" });
  }
};

// API endpoint to update table positions
export const updateTablePositions = async (req, res) => {
  const { siteId, tablePositions } = req.body; // Expecting siteId and tablePositions in the request body

  if (!siteId || !tablePositions) {
    return res.status(400).json({ message: "Site ID and table positions are required" });
  }

  try {
    // Loop through each table's position and update it
    const updatePromises = tablePositions.map(async (tablePos) => {
      const updatedTable = await tables.findOneAndUpdate(
        { tableNo: tablePos.tableNo, location: siteId }, // Ensure the table exists in the right site
        { $set: { x: tablePos.x, y: tablePos.y } },
        { new: true }
      );
      return updatedTable;
    });

    // Wait for all updates to complete
    const updatedTables = await Promise.all(updatePromises);

    return res.status(200).json({ message: "Table positions updated successfully", updatedTables });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ message: "Error updating table positions", error: error.message });
  }
};

export const deleteTables = async (req, res) => {
  console.log(req.params);
  let data = await tables.deleteOne(req.params);

  if (data) {
    res.send({ message: "tables data delete successfully" });
  } else {
    res.send({ message: "tables data cannot delete successfully" });
  }
};
