// Improved Daily Shift Report Controller
import orderitem from '../models/orderitem.js';
import emplyeeTime from '../models/employeetime.js';
import BillDenomination from '../models/denomination.js';
import {employee} from '../models/employee.js';

export const getDailyShiftReport = async (req, res) => {
  try {
    const { userId, employeeId, date } = req.query;
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'userId is required in query parameters'
      });
    }

    let targetDate;
    if (date) {
      targetDate = new Date(date);
    } else {
      targetDate = new Date();
      targetDate.setHours(0, 0, 0, 0); 
    }
    
    const startOfDay = new Date(targetDate);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(targetDate);
    endOfDay.setHours(23, 59, 59, 999);
    
    console.log('Checking date:', startOfDay, 'to', endOfDay);

    let employees = [];
    
    if (employeeId) {
      const emp = await employee.findById(employeeId);
      if (emp) {
        employees = [emp];
      }
    } else {
      employees = await employee.find({});
    }
    
    // If no employees found, return empty data with default structure
    if (employees.length === 0) {
      return res.status(200).json({
        success: true,
        message: `No employees found for ${targetDate.toDateString()}`,
        data: [],
        summary: {
          userId: userId,
          totalEmployees: 0,
          totalSales: 0,
          totalTransactions: 0,
          totalVariance: 0,
          date: targetDate.toDateString()
        }
      });
    }

    const shiftReports = [];
    let previousEndingDrawer = 0; // Track ending drawer from previous shift
    
    // Sort employees by shift start time to handle sequential shifts
    const employeesWithShifts = [];
    for (const emp of employees) {
      const employeeShift = await emplyeeTime.findOne({
        employeeId: emp._id,
        startDate: {
          $gte: startOfDay,
          $lte: endOfDay
        }
      });
      
      if (employeeShift) {
        employeesWithShifts.push({ employee: emp, shift: employeeShift });
      }
    }
    
    // Sort by start time
    employeesWithShifts.sort((a, b) => {
      const timeA = a.shift.startHour || '00:00';
      const timeB = b.shift.startHour || '00:00';
      return timeA.localeCompare(timeB);
    });
    
    // If no shifts found, return empty data with default structure
    if (employeesWithShifts.length === 0) {
      return res.status(200).json({
        success: true,
        message: `No shifts found for ${targetDate.toDateString()}`,
        data: [],
        summary: {
          userId: userId,
          totalEmployees: 0,
          totalSales: 0,
          totalTransactions: 0,
          totalVariance: 0,
          date: targetDate.toDateString()
        }
      });
    }

    for (let i = 0; i < employeesWithShifts.length; i++) {
      const { employee: emp, shift: employeeShift } = employeesWithShifts[i];
      
      console.log('Processing employee:', emp.name);
      
      const orders = await orderitem.find({
        userId: userId, 
        employeeId: emp._id,
        createdAt: {
          $gte: startOfDay,
          $lte: endOfDay
        }
      });

      let totalTransactions = 0;
      let totalAmount = 0;
      let cashSales = 0;

      orders.forEach(order => {
        if (order.Status !== 'cancelled') { 
          totalTransactions++;
          if (order.grandTotal) {
            totalAmount += order.grandTotal;
            
            if (order.PaymentStatus === 'cash' || 
                (order.paymentType && order.paymentType.toString().includes('cash'))) {
              cashSales += order.grandTotal;
            }
          }
        }
      });

      let openingAmount = 0;
      let endingAmount = 0;
      let variance = 0;
      
      if (i === 0) {
        // First shift: Opening float comes from opening_cash denomination entry
        const openingFloat = await BillDenomination.findOne({
          userId: userId, 
          employeeId: emp._id, 
          type: 'opening_cash',
          createdAt: {
            $gte: startOfDay,
            $lte: endOfDay
          }
        }).sort({ createdAt: 1 });
        
        if (openingFloat && openingFloat.totalCashOnhand !== undefined) {
          openingAmount = openingFloat.totalCashOnhand;
        } else {
          openingAmount = 0; // Default if no opening float recorded
        }
        
      } else {
   
        openingAmount = previousEndingDrawer;
      }
      
  
      const expectedEndingAmount = openingAmount + totalAmount;
      

      const endingDrawerEntry = await BillDenomination.findOne({
        userId: userId, 
        employeeId: emp._id, 
        createdAt: {
          $gte: startOfDay,
          $lte: endOfDay
        }
      }).sort({ createdAt: -1 });
      
      if (endingDrawerEntry) {

        if (endingDrawerEntry.overShortAmount !== undefined && endingDrawerEntry.overShortAmount !== null) {
          endingAmount = endingDrawerEntry.overShortAmount;
          variance = endingAmount - expectedEndingAmount;
          console.log(`Employee ${emp.name}: Manual ending drawer ${endingAmount}, Expected ${expectedEndingAmount}, Variance ${variance}`);
        } 
 
        else if (endingDrawerEntry.totalCashOnhand !== undefined && endingDrawerEntry.type !== 'opening_cash') {
          endingAmount = endingDrawerEntry.totalCashOnhand;
          variance = endingAmount - expectedEndingAmount;
          console.log(`Employee ${emp.name}: Ending drawer from totalCashOnhand ${endingAmount}, Expected ${expectedEndingAmount}, Variance ${variance}`);
        } 
  
        else {
          endingAmount = expectedEndingAmount;
          variance = 0;
          console.log(`Employee ${emp.name}: Using expected amount ${expectedEndingAmount}, no manual count found`);
        }
      } else {
     
        endingAmount = expectedEndingAmount;
        variance = 0;
        console.log(`Employee ${emp.name}: No denomination entry found, using expected amount ${expectedEndingAmount}`);
      }
      
      const shiftReport = {
        Cashier: employeeShift.empName || emp.name || 'N/A',
        ShiftTime: `${employeeShift.startHour || 'N/A'} - ${employeeShift.endHour || 'N/A'}`,
        Total: parseFloat(totalAmount.toFixed(2)),
        SalesTransactions: totalTransactions,
        OpeningFloat: parseFloat(openingAmount.toFixed(2)),
        EndingDrawer: parseFloat(endingAmount.toFixed(2)),
        Variance: parseFloat(variance.toFixed(2)),
        Date: targetDate.toDateString(),
        UserId: userId,
        EmployeeId: emp._id.toString(),
        CashSales: parseFloat(cashSales.toFixed(2)),
        ExpectedCash: parseFloat(expectedEndingAmount.toFixed(2))
      };
      shiftReports.push(shiftReport);
      previousEndingDrawer = endingAmount;
      console.log('Completed report for:', emp.name);
    }
    res.status(200).json({
      success: true,
      message: `Daily shift report for ${targetDate.toDateString()}`,
      data: shiftReports,
      summary: {
        userId: userId,
        totalEmployees: shiftReports.length,
        totalSales: parseFloat(shiftReports.reduce((sum, report) => sum + report.Total, 0).toFixed(2)),
        totalTransactions: shiftReports.reduce((sum, report) => sum + report.SalesTransactions, 0),
        totalVariance: parseFloat(shiftReports.reduce((sum, report) => sum + report.Variance, 0).toFixed(2)),
        totalCashSales: parseFloat(shiftReports.reduce((sum, report) => sum + (report.CashSales || 0), 0).toFixed(2)),
        date: targetDate.toDateString()
      }
    });

  } catch (error) {
    console.error('Error in daily shift report:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating daily shift report',
      error: error.message
    });
  }
};