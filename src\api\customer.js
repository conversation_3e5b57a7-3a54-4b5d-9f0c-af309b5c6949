import customer from '../models/customer.js';
import jwt from 'jsonwebtoken';


export const getCustomer = async (req, res) => {
    let filter = {}
    if (req.query.userId) {
        filter = { userId: req.query.userId.split(',') }
    }
    let customerData = await customer.find(filter)
    res.send(customerData);

}
//
// export const getCustomerById = async (req, res) => {
//     let customerData = await customer.findById(req.params)
//     res.send(customerData);
// }

export const getCustomerById = async (req, res) => {
   
    let data = await customer.findOne(req.params);
    res.send(data)
}
// export const searchCustomerByCardNo = async (req, res) => {
//     const { CardNo } = req.params;
//     try {
//         const customerData = await customer.find({
//             "CustomerLoyalty.CardNo": { $regex: new RegExp(String(CardNo), 'i') }
//         });
//         return res.send(customerData);
//     } catch (error) {
//         console.error('Error searching for customer:', error);
//         return res.status(500).send({ error: 'Internal Server Error' });
//     }
// };

// export const searchCustomer = async (req, res) => {
//     const { userId, FirstName, LastName, Email, Phone, Address, City, State, CustomerId  } = req.params
//     console.log('req.params: ', req.params);

//     let customerData = await customer.find({
//         "$and": [
//             { userId }
//             ,
//             {
//                 "$or": [
//                     { FirstName: { $regex: new RegExp(String(FirstName),'i') } },
//                     { LastName: { $regex: new RegExp(String(LastName),'i') } },
//                     { Email: { $regex: String(Email) } },
//                     { Phone: { $in: Number(Phone) } },
//                     { Address: { $regex: String(Address) } },
//                     { City: { $regex: String(City) } },
//                     { State: { $regex: String(State) } },
//                     // { PostalCode: { $in: Number(!isNaN(PostalCode), true) } },
//                     { CustomerId: { $in: Number(!isNaN(CustomerId), true) } },
//                     { "CustomerLoyalty.CardNo": { $regex: new RegExp(String(FirstName)) } },
//                 ]
//             }
//         ]
//     })
//     console.log('customerData : ', customerData );
    
//     return res.send(customerData);

// }

export const postCustomer = async (req, res) => {
    const { CustomerId, userId, FirstName, LastName, Phone, Address,isActive, City, State,Email, Membership, CustomerLoyalty } = req.body;
    try{
     const lastCustomer = await customer.findOne(
          { userId },
          {},
          { sort: { _id: -1 } }
        );
        console.log("last customer : ", lastCustomer);
    
        let numericCount = 1; // Default value if no previous data
    
        if (lastCustomer && lastCustomer.CustomerId) {
          const match = lastCustomer.CustomerId.match(/\d+$/);
          if (match) {
            numericCount = parseInt(match[0], 10) + 1;
        }
        }
    
        const customerId = `CUS-${String(numericCount).padStart(4, "0")}`;
    const customerData = await new customer({ CustomerId: customerId, userId, FirstName, LastName, Phone, Address,isActive, City, State,Email, Membership, CustomerLoyalty });
    await customerData.save().then(result => {
        console.log(result, "Customer data save to database")
        res.json({
            CustomerId: result.CustomerId,
            FirstName: result.FirstName,
            userId: result.userId,
            LastName: result.LastName,
            Phone: result.Phone,
            Address: result.Address,
            // Address2: result.Address2,
            City: result.City,
            State: result.State,
            Email : result.Email,
            Membership: result.Membership,
            CustomerLoyalty: result.CustomerLoyalty,
            isActive:result.isActive,
            _id: result._id 
        })
    })
  }catch(err){
        res.status(400).send('unable to save database');
        console.log(err)
    }
}


export const registerByFacebook = async (req, res) => {
    try {
      const { FirstName, LastName, Email, profile_pic, facebookId } = req.body;
  
      // Check if the user already exists
      let existingUser = await customer.findOne({ Email, FirstName, LastName,profile_pic,facebookId });
  
      if (existingUser) {
        // If user exists, load the secret key from an environment variable
        const secretKey = process.env.JWT_SECRET_KEY || 'defaultSecretKey';
  
        // Generate a token and send it in the response
        const token = jwt.sign({ userId: existingUser._id }, secretKey, { expiresIn: '1h' });
  
        return res.status(200).json({
          status: true,
          data: {
            user: existingUser,
            token: token,
          },
          message: "User already registered, signed in successfully",
        });
      }else{
 // If user doesn't exist, save customer data to the database
 const customerData = new customer({ FirstName, LastName, Email, profile_pic, facebookId });
 const result = await customerData.save();

 console.log(result, "Customer data saved to the database");

 // Generate a token for the newly registered user
 const secretKey = process.env.JWT_SECRET_KEY || 'defaultSecretKey';
 const token = jwt.sign({ userId: result._id }, secretKey, { expiresIn: '1h' });

 res.status(200).json({
   status: true,
   data: {
     user: result,
     token: token,
   },
   message: "User registered and signed in successfully",
 });
      }
  
     
    } catch (err) {
      console.log(err);
      return res.status(500).json({ message: "Something went wrong" });
    }
  };
  








export const updateCustomer = async (req, res) => {

    console.log(req.params.id)
    let data = await customer.findByIdAndUpdate(
        { _id: req.params._id }, {
        $set: req.body
    },

        { new: true }
    );
    if (data) {
        res.send({data, message: "customer data updated successfully" });
    }
    else {
        res.send({ message: "customer data cannot be updated successfully" })
    }
}
export const deleteCustomer = async (req, res) => {
    console.log(req.params)
    let data = await customer.deleteOne(req.params)
    if (data) {
        res.send({ message: "customer data delete successfully" });
    }
    else {
        res.send({ message: "customer data cannot delete successfully" })
    }
}
