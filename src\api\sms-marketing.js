import sendSms from '../middlewares/send-sms.js';
import smsMarketing from '../models/sms-marketing.js';
import SendOTP from '../models/OTPsendNumber.js';
//reset password throght email

export const getsmsMarketing = async (req, res) => {
    let filter={}
    if(req.query.userId){
     filter={userId:req.query.userId.split(',')}
    }
    let data = await smsMarketing.find(filter);
    res.send(data)
}

 export const postSmsMarketing = async (req, res) => {
    console.log("request", req)
    try { 
        const {number,message, userId}=req.body;
        const smsMarketingS=await new smsMarketing({number, message, userId})
        const data= await smsMarketingS.save();
         
        await sendSms(number,message);
        return res.json({data, message: `link send to your mobile number` })

    } catch (error) {
        res.send("An error occured");
        console.log(error);
    }

};


export const POstOTPSend = async (req, res) => {
    try {
        const { number } = req.body;
         // Generate a random number between 100000 and 999999
         const otp1 = Math.floor(100000 + Math.random() * 900000);
        const OtpToString=  otp1.toString(); // Convert the number to a string
        const otp  =  `${'Your PatronWorks verification code is:'}  ${OtpToString}`
        const postOtp = await new SendOTP({ number, otp });
        const data = await postOtp.save();
        console.log("res.otp", req.body)
        await sendSms(number, otp);
        // Return success message along with number and message
        const responseMessage = `OTP sent to ${number}`;
        return res.json({ number:number,otp:otp, message: responseMessage });
       
    } catch (error) {
        console.log(error);
        return res.status(500).json({ error: "An error occurred" });
    }
}


