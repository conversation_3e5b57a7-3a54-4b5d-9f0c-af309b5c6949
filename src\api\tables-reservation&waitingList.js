import tableSelect from '../models/tables-reservation&waitingList.js';

export const getReservedTables=async(req,res)=>{
    let filter={}
    if(req.query.userId){
        filter={userId:req.query.userId.split(',')}
    }
    const reservation=await tableSelect.find(filter).populate('table');
    res.send(reservation);
}
export const getReservedTablebyId=async(req,res)=>{
    const tableByid=await tableSelect.find(req.params).populate('table');
    res.send(tableByid);
}
export const postReservedTables=async(req,res)=>{
    const {fullName,address,email,contactNumber,reserveDate,reserveTime,visitorNote,site,takenBy,pagerNumber,table,partySize,userId}=req.body;
    const ReservationAndWaitingList=await new tableSelect({fullName,address,email,contactNumber,reserveDate,reserveTime,visitorNote,site,takenBy,pagerNumber,table,userId,partySize})
    await ReservationAndWaitingList.save().then(result=>{
        console.log(result, "Reservations data save to database")
        res.json({
            fullName:result.fullName,
            email:result.email,
            address:result.address,
            contactNumber:result.contactNumber,
            reserveDate:result.reserveDate,
            reserveTime:result.reserveTime,
            visitorNote:result.visitorNote,
            site:result.site,
            takenBY:result.takenBy,
            pagerNumber:result.pagerNumber,
            table:result.table,
            partySize:result.partySize,
            userId:result.userId
        })
    }).catch(err => {
        res.status(400).send('unable to save database');
        console.log(err)
    })
}
export const updateReservedTable= async (req, res) => {

    console.log(req.params._id)
    let data = await tableSelect.findByIdAndUpdate(
        { _id: req.params._id }, {
        $set: req.body
    },
        { new: true }
    );
    if (data) {
        res.send({ message: "Reservations data updated successfully" });
    }
    else {
        res.send({ message: "Reservations data cannot be updated successfully" })
    }
}
export const deleteReservedTable= async (req, res) => {
    console.log(req.params)
    let data = await tableSelect.deleteOne(req.params)
    if (data) {
        res.send({ message: "Reservations data delete successfully" });
    }
    else {
        res.send({ message: "Reservations data cannot delete successfully" })
    }
}
export const deleteReservedbyTableId= async (req, res) => {
    const { table } = req.query
    console.log("table", table);
    let orderItem = await tableSelect.findOneAndDelete({
        "$or": [
            { table: table }
        ]
    })
    if (orderItem) {
        res.send({ message: "Reservation data by tableId delete successfully" });
    } else {
        res.send({ message: "Reservation data cannot delete successfully" })
    }
}