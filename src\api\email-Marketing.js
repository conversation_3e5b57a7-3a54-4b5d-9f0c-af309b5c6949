import sendMail from '../middlewares/send-email.js';
import emailMarketing from '../models/emailMarketing.js';
import axios from 'axios';

//reset password throght email

export const getemailMarketing = async (req, res) => {
    let filter = {}
    if (req.query.userId) {
        filter = { userId: req.query.userId.split(',') }
    }
    let data = await emailMarketing.find(filter);
    res.send(data)
}

export const postEmailMarketing = async (req, res) => {
    try {
        const { email, subject, message, userId } = req.body;
        const emailMarketingS = await new emailMarketing({ subject, email, message, userId })
        const data = await emailMarketingS.save();

        await sendMail(email, subject, `<h4>${message}</h4> `);
        return res.json(data)

    } catch (error) {
        res.send("An error occured");
        console.log(error);
    }
};

export const deleteEmailMarketing = async (req, res) => {
    console.log(req.params)
    const displayId = req.params
    let data = await emailMarketing.deleteOne(displayId)
    if (data) {
        res.send({ message: " email delete successfully" });
    }
    else {
        res.send({ message: "email cannot delete successfully" })
    }
}// export const Mailchimp = async (req, res) => {
//     try {
//         const url = "https://us17.api.mailchimp.com/3.0/lists/402f520154/members";
//         const data = req.body;
//         const checkEmailUrl = `https://us17.api.mailchimp.com/3.0/lists/402f520154/members/${encodeURIComponent(data.email_address)}`;

//         const checkResponse = await axios.get(checkEmailUrl, {
//             headers: {
//                 'Authorization': 'Bearer *************************************'
//             }
//         });

//         if (checkResponse.status === 200) {
//             return res.status(400).json({ error: 'Email already exists in the MailChimp list.' });
//         }

//         const response = await axios.post(url, data, {
//             headers: {
//                 'Content-Type': 'application/json',
//                 'Authorization': 'Bearer *************************************'
//             }
//         });

//         const confirmationMessage = 'Email added successfully to MailChimp list.';
//         res.json({ confirmation: confirmationMessage, responseData: response.data });
//     } catch (error) {
//         console.error('Error occurred:', error.message);
//         res.status(500).json({ error: 'An error occurred while calling the MailChimp API' });
//     }
// };

export const Mailchimp = async (req, res) => {
    try {
        const url = "https://us17.api.mailchimp.com/3.0/lists/402f520154/members";
        const data = req.body;
        console.log("req.body:", req.body);
        const email = encodeURIComponent(data.email_address);
        const checkEmailUrl = `https://us17.api.mailchimp.com/3.0/lists/402f520154/members/${email}`;

        // Send GET request to check if the email exists
        let checkResponse;
        try {
            checkResponse = await axios.get(checkEmailUrl, {
                headers: {
                    'Authorization': 'Bearer *************************************'
                }
            });
            console.log("check response status:", checkResponse.status);

            if (checkResponse.status === 200) {
                // Email already exists, return error response
                console.log("Email already exists in the MailChimp list.");
                return res.status(200).json({ message: 'Email added successfully to MailChimp list' });
            }
        } catch (error) {
            // If there's an error, check if it's a 404 response
            if (error.response && error.response.status === 404) {
                // Email doesn't exist, proceed to add it to the list
                try {
                    const response = await axios.post(url, data, {
                        headers: {
                            'Authorization': 'Bearer *************************************',
                            'Content-Type': 'application/json'
                        }
                    });

                    console.log("post response:", response.data);
                    const confirmationMessage = 'Email added successfully to MailChimp list.';
                    return res.json({ confirmation: confirmationMessage, responseData: response.data });
                } catch (error) {
                    console.error('Error occurred while adding email to MailChimp list:', error.message);
                    // Handle the error appropriately
                    return res.status(500).json({ message: 'An error occurred while adding email to MailChimp list' });
                }
            } else {
                // If it's not a 404 response, handle the error appropriately
                console.error('Error occurred while checking email:', error);
                return res.status(500).json({ message: 'An error occurred while checking the email' });
            }
        }
    } catch (error) {
        console.error('Error occurred:', error.message);
        return res.status(500).json({ message: 'An error occurred while calling the MailChimp API' });
    }
};
