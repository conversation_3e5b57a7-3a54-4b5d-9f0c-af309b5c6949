import twilio from "twilio";
import { configDotenv } from "dotenv"; 
import express from 'express'


const app=express()
configDotenv();
const twilioClient=twilio(process.env.TWILIO_ACCOUNT_ID,process.env.TWILIO_AUTH_KEY)

// export const twilioVoice= async (req, res) => {

//   const twiml = new twilio.twiml.VoiceResponse();

//   const gather = twiml.gather({
//       input: 'speech',
//       action: '/process-speech',
//       speechTimeout: 'auto',
//       speechModel: 'default'
//   });
//   gather.say('Welcome to our service. How can I assist you today?');

//   twiml.redirect('/process-speech');

//   res.type('text/xml');
//   res.send(twiml.toString());
// }   


export const CreateTranscript = async (req, res) => {
  try {
    const serviceSid = 'GAf48d616d7614c4606f7b34609ec6698b'; 
    const channel = {};

    const transcript = await twilioClient.media.transcriptions.create({
      serviceSid: serviceSid,
      channel: channel,
    });

    console.log(transcript.sid);
    res.status(200).send({ success: true, transcriptSid: transcript.sid });
  } catch (error) {
    console.error(error);
    res.status(500).send({ success: false, error: error.message });
  }
}

// export const ProcessSpeech= async (req, res) => {
//   const speechResult = req.body.SpeechResult;
//   console.log(`Customer said: ${speechResult}`);

//   // Process the speech result with your AI agent (e.g., Dialogflow)
//   const aiResponse = await getAIResponse(speechResult);

//   const twiml = new twilio.twiml.VoiceResponse();
//   twiml.say(aiResponse);

//   // Record the call
//   twiml.record({ transcribe: true, maxLength: 120, playBeep: true });

//   res.type('text/xml');
//   res.send(twiml.toString());
// };

