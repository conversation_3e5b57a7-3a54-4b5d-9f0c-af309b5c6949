import express from "express";
const routes = express.Router();

import {
getCancelSub,
getCancelSubs,
postCancelSub,
updateCancelSub,
deleteCancelSub
} from "../api/cancel-subscription.js"

routes.get('/cancelsub', getCancelSubs)
routes.get('/cancelsub/:_id', getCancelSub)

routes.post('/cancelsub',  postCancelSub)
routes.put('/cancelsub/:_id',  updateCancelSub)
routes.delete('/cancelsub/:_id', deleteCancelSub)


export default routes