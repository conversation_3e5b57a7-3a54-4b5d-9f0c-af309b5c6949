import product from '../models/product.js';
import axios from 'axios';
// import NodeCache fron 'node-cache';
import NodeCache from 'node-cache';

const productCache = new NodeCache({ stdTTL: 3600 });

    // export const getProduct = async (req, res) => {
    //     try {
    //         let filter = {};

    //         if (req.query.categoryId) {
    //             filter.categoryId = { $in: req.query.categoryId.split(',') };
    //         }
    //         if (req.query.userId) {
    //             filter.userId = { $in: req.query.userId.split(',') };
    //         }

    //         // Fetch all product data first (EXCLUDING Product_pic)
    //         let productData = await product
    //             .find(filter, { name: 1, price: 1,totalQuantity:1,ProductId:1, categoryId: 1, userId: 1, isLock: 1, hasPicture:1, barCode:1,retailPrice:1,active:1 }) // Exclude Product_pic
    //             .populate('categoryId', '_id name')
    //             .populate('categoryParents', 'name')
    //             .populate('userId', '_id')
    //             .populate('order', '_id status')
    //             .populate('unit', '_id name')
    //             .populate('ingredient.ingredientId', '_id IngredientName',)
    //             .populate({ 
    //                 path: "reviewId", 
    //                 select: "_id rating comment", 
    //                 populate: { path: "customerId", model: "customer", select: "name email" }
    //             })
    //             .lean()
    //             .exec();

    //         // Filter only unlocked products
    //         let unlockedProducts = productData.filter((item) => item.isLock === false);

    //         // Fetch Product_pic separately
    //         const productIds = unlockedProducts.map((p) => p._id);
    //         let productPics = await product.find({ _id: { $in: productIds } }, { _id: 1, Product_pic: 1 }).lean().exec();

    //         // Merge Product_pic into the original response
    //         let productMap = new Map(productPics.map(p => [p._id.toString(), p.Product_pic]));
    //         unlockedProducts = unlockedProducts.map(p => ({
    //             ...p,
    //             Product_pic: productMap.get(p._id.toString()) || null
    //         }));

    //         res.json(unlockedProducts);
    //     } catch (error) {
    //         res.status(500).json({ message: 'Server error', error });
    //     }
    // };


    export const getProduct = async (req, res) => {
  try {
    const filter = {};

    if (req.query.categoryId) {
      filter.categoryId = { $in: req.query.categoryId.split(',') };
    }
    if (req.query.userId) {
      filter.userId = { $in: req.query.userId.split(',') };
    }

    let productData = await product
      .find(filter, {
        name: 1,
        price: 1,
        totalQuantity: 1,
        ProductId: 1,
        categoryId: 1,
        userId: 1,
        isLock: 1,
        hasPicture: 1,
        barCode: 1,
        retailPrice: 1,
        active: 1,
        ingredient: 1  // <--- include ingredient array with quantity here
      })
      .populate('categoryId', '_id name')
      .populate('categoryParents', 'name')
      .populate('userId', '_id')
      .populate('order', '_id status')
      .populate('unit', '_id name')
      .populate({
        path: 'ingredient.ingredientId',
        select: '_id IngredientName'
      })
      .populate({
        path: 'reviewId',
        select: '_id rating comment',
        populate: { path: 'customerId', model: 'customer', select: 'name email' }
      })
      .lean()
      .exec();

    // Filter unlocked products only
    let unlockedProducts = productData.filter(item => item.isLock === false);

    // Filter out ingredient entries where ingredientId is null
    unlockedProducts = unlockedProducts.map(prod => {
      if (Array.isArray(prod.ingredient)) {
        prod.ingredient = prod.ingredient.filter(i => i.ingredientId !== null);
      }
      return prod;
    });

    // Fetch Product_pic separately
    const productIds = unlockedProducts.map(p => p._id);
    const productPics = await product
      .find({ _id: { $in: productIds } }, { _id: 1, Product_pic: 1 })
      .lean()
      .exec();

    const productPicMap = new Map(productPics.map(p => [p._id.toString(), p.Product_pic]));
    unlockedProducts = unlockedProducts.map(p => ({
      ...p,
      Product_pic: productPicMap.get(p._id.toString()) || null
    }));

    res.json(unlockedProducts);
  } catch (error) {
    console.error('Error in getProduct:', error);
    res.status(500).json({ message: 'Server error', error });
  }
};




export const getProductpatronpal = async (req, res) => {
    try {
        let filter = {};
        if (req.query.categoryId) {
            filter.categoryId = { $in: req.query.categoryId.split(',') };
        }
        if (req.query.userId) {
            filter.userId = { $in: req.query.userId.split(',') };
        }

        // Generate cache key based on userId and categoryId
        const cacheKey = `${req.query.userId}-${req.query.categoryId}`;

        // Check cache for existing data
        let cachedData = productCache.get(cacheKey);
        if (cachedData) {
            return res.send(cachedData); // Return cached data if available
        } else {
            // Fetch data from MongoDB
            let productData = await product.find(filter)
                .populate({ path: 'categoryId', populate: { path: 'parentId', model: 'parentcategory' } })
                .populate('categoryParents', 'name')
                .populate('userId')
                .populate('order')
                .populate('unit')
                .populate('ingredient.ingredientId')
                .populate({ path: 'reviewId', populate: { path: 'customerId', model: 'customer' } });

            // Filter data where isLock is false
            let isLockFiltered = productData.filter((item) => item.isLock === false);

            // Cache the filtered data in node-cache with a specific TTL (adjust ttlSeconds as needed)
            const ttlSeconds = 60 * 5; // Cache for 5 minutes (adjust as per your requirement)
            productCache.set(cacheKey, isLockFiltered, ttlSeconds);

            // Return the filtered data
            res.send(isLockFiltered);
        }
    } catch (error) {
        console.error(error); // Log the error for debugging
        res.status(500).send({ error: error.message }); // Send an error response
    }
};

export const getProductwithParentcategory = async (req, res) => {
    let filter = {}
    if (req.query.categoryId) {
        filter = { categoryId: req.query.categoryId.split(',') }
    }
    let filter2 = {}
    if (req.query.userId) {
        filter = { userId: req.query.userId.split(',') }
    }
    let productData = await product.find(filter, filter2).populate({ path :'categoryId', populate: { path: "parentId", model: "parentcategory"}}).populate('categoryParents', 'name').populate('userId').populate('order').populate('unit').populate('ingredient.ingredientId').populate({ path: "reviewId", populate: { path: "customerId", model: "customer"} })
    let islock = productData.filter((item) => item.isLock == false)
    res.send(islock);

}
export const getFilteredProduct = async (req, res) => {
    let productData = await product.find().populate('categoryId').populate('categoryParents', 'name').populate('userId').populate('order').populate('unit').populate('ingredient.ingredientId').populate({ path: "reviewId", populate: { path: "customerId", model: "customer"} })

    let ActiveProduct = productData?.filter((item) => item.userId?.isActive === true)
    res.send(ActiveProduct);
}
export const getProductById = async (req, res) => {
    let productData = await product.findOne(req.params).populate('userId').populate('categoryId', 'name').populate('order').populate('categoryParents', 'name').populate('unit').populate('ingredient.ingredientId').populate({ path: "reviewId", populate: { path: "customerId", model: "customer"} })
    res.send(productData);
}
export const getProductByKey = async (req, res) => {
    let productData = await product.find({
        "$or": [{
            name: { $regex: req.params.key }
        }]
    }).populate('categoryId', 'name').populate('order').populate('categoryParents', 'name').populate('unit')
    let isLock = productData.filter((item) => item.isLock == false)
    res.send(isLock);
}

export const postProduct = async (req, res) => {
    const { lavel, rows, cols, categoryParents, barCode, name, ingredient, price, retailPrice,discountPrice, shortDescription, fullDescription, order, active, categoryId, hasPicture, productPictureId, totalQuantity, productId, productType, userId, unit } = req.body;
    // let ingredientparse= JSON.parse(req.body?.ingredient)
    const ingredientparse = req.body.ingredient ? JSON.parse(req.body.ingredient) : undefined;
    const courseDate1 = req.body.courseDate ? JSON.parse(req.body.courseDate) : undefined;
    // return console.log("product data : ",req.body)RS
    const Product_pic = req.file ? req.file.location : req.body.Product_pic || null;
    console.log('Product_pic: ', Product_pic);
    try {
        const lastProduct = await product.findOne({ userId }, {}, { sort: { '_id': -1 } });
        const lastProductCount = lastProduct ? (lastProduct.ProductId || 0) : 0;
        let numericCount
        if (lastProductCount != 0) {

            numericCount = parseInt(lastProductCount.slice(2), 10) + 1;
        } else {

            numericCount = Number("0001")
        }
        const ProductId = `PR${numericCount.toString().padStart(4, '0')}`


        const productData = new product({ lavel, rows, cols, categoryParents, ingredient: ingredientparse, totalQuantity, barCode, name, price, retailPrice, discountPrice, shortDescription, fullDescription, order, active, categoryId, hasPicture, productPictureId, productId, productType, userId, Product_pic, unit, ProductId, courseDate: courseDate1 })
        // .populate('categoryParents').populate('categoryId').populate('unit');
        const savedProduct = await productData.save();
        const populatedProduct = await product.findById(savedProduct._id)
        .populate('categoryParents')
        .populate('categoryId')
        .populate('unit');

    res.json(populatedProduct);
        // res.json({
        //     _id: savedProduct._id,
        //     lavel: savedProduct.lavel,
        //     cols: savedProduct.cols,
        //     rows: savedProduct.rows,
        //     unit: savedProduct.unit,
        //     ProductId: savedProduct.ProductId,
        //     ingredient: savedProduct.ingredientparse,
        //     categoryParents: savedProduct.categoryParents,
        //     barCode: savedProduct.barCode,
        //     name: savedProduct.name,
        //     price: savedProduct.price,
        //     retailPrice: savedProduct.retailPrice,
        //     totalQuantity: savedProduct.totalQuantity,
        //     order: savedProduct.order,
        //     active: savedProduct.active,
        //     categoryId: savedProduct.categoryId,
        //     hasPicture: savedProduct.hasPicture,
        //     productPictureId: savedProduct.productPictureId,
        //     productId: savedProduct.productId,
        //     productType: savedProduct.productType,
        //     userId: savedProduct.userId,
        //     Product_pic: savedProduct.Product_pic,
        //     courseDate: savedProduct.courseDate,
        //     discountPrice:savedProduct.discountPrice

        // });
    } catch (error) {
        console.error('Error:', error);
        res.status(400).send('Unable to save to database');
    }
}

export const updateProduct = async (req, res) => {
    const { lavel, rows, cols, categoryParents, barCode, name, Product_pic, price, retailPrice, shortDescription, fullDescription, order, active, categoryId, hasPicture, productPictureId, totalQuantity, productId, productType, userId, unit, discountPrice} = req.body;
    let ingredientparse = req.body.ingredient ? JSON.parse(req.body.ingredient) : undefined;
    const courseDate1 = req.body.courseDate ? JSON.parse(req.body.courseDate) : undefined;


    const Product = await product.findById({ _id: req.params._id });
    if (!Product) {
        return res.status(404).send({ message: "Product data not found." });
    }
    let userId1
    if (Product.price != price) {
        // let ingredientparse= JSON.parse(req.body.ingredient)
        let ingredientparse = req.body.ingredient ? JSON.parse(req.body.ingredient) : undefined;


        await product.findByIdAndUpdate({ _id: req.params._id }, {
            $set: { "isLock": true, ingredient: ingredientparse }
        }, { new: true }).then(result => {
            userId1 = result.userId
        }).catch(error => {
            console.error(error)
            res.status(500).send({ message: "Error updating product data." });
        });
        const { lavel, rows, cols, categoryParents, barCode, name, Product_pic, price, retailPrice, shortDescription, fullDescription, order, active, categoryId, hasPicture, productPictureId, totalQuantity, productId, productType, userId, unit,reviewId,discountPrice } = req.body;

        const newProduct = await new product({ lavel, rows, cols, categoryParents, totalQuantity,ingredient:ingredientparse, barCode, name, price, retailPrice, shortDescription, fullDescription, order, active, categoryId, hasPicture, productPictureId, productId, productType, userId: userId1, Product_pic, unit,courseDate:courseDate1 ,reviewId,discountPrice }, );
        await newProduct.save().then(result => {
            return res.send({ result, message: "Product data saved successfully." });
        }).catch(error => {
            console.error(error);
            return res.status(500).send({ message: "Error saving product data." });
        });
    } else {
        // if (Product.price) {
        let ingredientparse = req.body.ingredient ? JSON.parse(req.body.ingredient) : undefined;
        let courseDate2 = req.body.courseDate ? JSON.parse(req.body.courseDate) : undefined;
       let Product_pic
        if (req.file) {
         Product_pic = req.file ? req.file.location : req.body.Product_pic || null;
        }
        if (req.body.Product_pic) {
         Product_pic = req.body.Product_pic || null;
        }
        const updateObject = {
            ...req.body,
            Product_pic: Product_pic,
            isLock: false,
            ingredient: ingredientparse,
            courseDate: courseDate2,
        };
        await product.findByIdAndUpdate({ _id: req.params._id }, updateObject, { new: true })
            .then(result => {
                return res.send({ result, message: "Product data updated successfully." });
            })
            .catch(error => {
                console.error(error);
                return res.status(500).send({ message: "Error updating product data." });
            });
    }
}
export const deleteProduct = async (req, res) => {
    console.log(req.params)
    let data = await product.deleteOne(req.params)
    if (data) {
        res.send({ message: "product data delete successfully" });
    }
    else {
        res.send({ message: "product data cannot delete successfully" })
    }
}
export const Search=async (req, res) => {
    const { q } = req.query;
    const apiKey = 'AIzaSyCoMcchRADP3TCbxTP0yTUrUvPbdGO0Uw4'; // Replace with your actual API key
    const cx = 'f2fa409473e5b4f42'; // Replace with your actual Search Engine ID
  
    try {
      const response = await axios.get('https://www.googleapis.com/customsearch/v1', {
        params: { q, key: apiKey, cx },
      });
  
      res.json(response.data);
    } catch (error) {
      console.error(error);
      res.status(500).send('Internal Server Error');
    }
  };
  