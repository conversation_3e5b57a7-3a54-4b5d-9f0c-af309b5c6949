import { User, superUser } from '../models/User.js'
import jwt from "jsonwebtoken";
import sendMail from '../middlewares/send-email.js';
import express from 'express';
const router = express.Router()
function generateRandomUserId() {
  const min = 10000000;
  const max = 99999999;
  return Math.floor(Math.random() * (max - min + 1)) + min;
}
router.post('/', async (req, res) => {
  const { firstname, lastname, email, password, businessName, role, phone, google, googleId } = req.body
  try {
    const user = await User.findOne({ email }) || await superUser.findOne({ email })
    if (user) {
      return res.status(400).send({ message: "User with this email already exists." })
    } else if (!user) {
      const userId = generateRandomUserId();
      const token = jwt.sign({ userId, firstname, lastname, email, phone, password, businessName, role, google, googleId }, process.env.JWT_SECRET, { expiresIn: '20min' })
      // const link = process.env.NODE_ENV === 'production' ?
        // `https://patronworks.net/auth/activation/${token}` :
      //   `https://dev.patronworks.net/auth/activation/${token}`;
      const link = `https://patronworks.net/auth/activation/${token}`;
      const emailHtml = `<html>

        <head>
            <style>
            .top {
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .top img {
                width: 60%;
                margin:auto;
            }

            .container {
                width: 50%;
                margin: auto;
                font-family: 'Poppins', sans-serif;
            }

            .maindivdata {
                padding: 2rem 4rem;
                border: 1px solid lightgray;
                font-family: 'Poppins', sans-serif;
            }

            .email {
                font-size: 1rem;
            }

            p{
              color:black;
              margin-bottom:2rem;
            }
            a {
                margin-bottom: 2rem;
                text-decoration:none;
            }

            .link {
                margin-bottom: 2rem;
                cursor: pointer;
                color: rgb(6, 138, 245);
                text-decoration: none;
            }

            .btn {
                background-color: rgb(6, 138, 245);
                color: white !important;
                font-size: 14px;
                width: 100%;
                border: none;
                padding:15px;
                border-radius: 5px;
                margin-bottom: 2rem;
                margin:auto;
                cursor: pointer;
                text-decoration: none;
                box-sizzing:border-box;
            }
            .btn a {
             margin-bottom:0;
              color: white;
              padding:15px 30px;
              cursor: pointer;
              text-decoration: none;
          }
           .btn:hover {
            background-color: #0056b3;
           }

            .footer {
                display: flex;
                justify-content: space-between;
                margin-top: 1rem;
            }

            .footer .shortimg {
                width: 20px;
                height: 20px;
            }

            .footer .logo {
                width: 60%;
            }

          @media screen and (max-width: 900px) {
              .container {
                  width: 100%;
                  margin: 0px;
              }
  
              .maindivdata {
                  padding: 2rem 10px;
              }
  
              .btn {
                  font-size: 12px;
              }
          }
        </style>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
      </head>
      <body>
          <div class="container">
              <div style="font-family: Arial, Helvetica, sans-serif; ">
                  <div style="width: auto; height: 4rem;background-color: rgb(6, 138, 245); "></div>
                  <div class="maindivdata">
                      <div class="top"> 
                          <img src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281194/WhatsApp_Image_2023-07-25_at_3.32.42_PM_gswsmd.jpg">
                      </div>
                      <p class="email">Dear <strong>${email}</strong>,</p>
                      <p>We are excited to have you join us at PatronWorks.</p>
                      <p>To ensure the security of your account and provide you with a smooth experience, we need to verify the email address associated with your account.</p>
                      <p>Please click the button below to verify your email address:</p>
                      <button class="btn"><a class="verify" href="${link}">VERIFY EMAIL</a></button>
                      <p>This link will redirect you to a page where your email address will be automatically verified. Once this step is completed, you will have access to all the available features of PatronWorks.</p>
                      <p>If the link is not clickable or clicking doesn't work, please copy the link provided below, paste it into the URL bar, and press Enter.</p>
                      <a class="link" href="${link}">${link}</a><br>
                      <b>This is your login id: ${userId}</b>
                      <p>This verification step ensures that we can reliably communicate with you and adds an extra layer of security to your account.</p>
                      <p>We look forward to serving you.</p>
                      <p>If you have any questions or require assistance, don't hesitate to contact our support team at <strong><EMAIL>.</strong></p>
                      <p>Thank you for choosing PatronWorks for your business!</p>
                      <hr>  
                      <div class="footer">
                          <div>
                              <img class="logo" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281267/WhatsApp_Image_2023-07-25_at_3.33.32_PM_xnwnuy.jpg">
                          </div>
                          <div style="display:flex; margin-left:45%;">
                              <a style="margin-right:10px;" href="https://www.linkedin.com/company/patronworks/">
                                  <img class="shortimg" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439086/WhatsApp_Image_2023-07-27_at_11.12.37_AM_1_whbn0t.jpg" alt="LinkedIn">
                              </a>
                              <a href="https://www.facebook.com/patronworks">
                                  <img class="shortimg" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439056/WhatsApp_Image_2023-07-27_at_11.12.37_AM_yedkyi.jpg" alt="Facebook">
                              </a>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </body>
        </html>`
      await sendMail(email, "Account Activation Link", emailHtml);

      return res.status(200).json({ message: "Account Verification Link Send To Ur Account" })

    }
  } catch (error) {
    res.send("An error occured");
    console.log(error);
  }
}
)
router.post('/:token', async (req, res) => {
  try {
    const { token } = req.params;
    console.log("token:", token);

    if (!token) {
      return res.status(400).json({ message: "Token is missing" });
    }

    jwt.verify(token, process.env.JWT_SECRET, async function (err, decodedToken) {
      if (err) {
        return res.status(400).json({ message: "Token is invalid or expired" });
      }

      const { firstname, lastname, email, password, role, phone, businessName, userId, google, googleId } = decodedToken;
      console.log("Decoded Token:", decodedToken);

      const userRegister = await User.findOne({ email });
      if (userRegister) {
        return res.status(400).json({ message: "This user is already registered" });
      }

      // if (role === 'admin') {
      //   const newUser = new User({ firstname, lastname, email, password, phone, businessName, role, userId, google, googleId });
      //   const savedUser = await newUser.save();
      //   console.log('savedUser: ', savedUser);

      //   if (savedUser) {
      //     const recipients = "<EMAIL>,<EMAIL>, ";
      //     const subject = "New Account Created";
      //     const message = `
      //                       <p>Hello,</p>
      //                       <p>Exciting news! A new account has been successfully created on PatronWorks.net.</p>
      //                       <p>Please find the details of the new user below:</p>
      //                       <ul>
      //                         <li><b>FullName:</b>${firstname} ${lastname} </li>
      //                         <li><b>Email:</b>${email}</li>
      //                         <li><b>Phone Number:</b> ${phone}</li>
      //                         <li><b>Business Name:</b> ${businessName}</li>
      //                         <li><b>Assigned Role:</b> ${role}</li>
      //                       </ul>
      //                       <p>We welcome ${firstname} ${lastname} to our platform and look forward to PatronWorks being a great companion to his business.</p>
      //                       <p>Cheers,<br>Patronworks.net</p>`;
      //     sendMail(recipients, subject, `<h4>${message}</h4>`);
      //     return res.send({ message: "Account Verified: Thanks For Registering User", userId: savedUser._id });
      //   } else {
      //     return res.status(400).json({ error: "Cannot register user at the moment!" });
      //   }
      // }

      if (role === 'admin') {
        const newUser = new User({ firstname, lastname, email, password, phone, businessName, role, userId, google, googleId });
        const savedUser = await newUser.save();
        // console.log('savedUser: ', savedUser);

        if (savedUser) {
          // Admin Notification Email
          const adminRecipients = "<EMAIL>,<EMAIL>,<EMAIL>, <EMAIL>";
          const adminSubject = "New Account Created";
          const adminMessage = `
      <p>Hello,</p>
      <p>Exciting news! A new account has been successfully created on PatronWorks.net.</p>
      <p>Please find the details of the new user below:</p>
      <ul>
        <li><b>Full Name:</b> ${firstname} ${lastname}</li>
        <li><b>Email:</b> ${email}</li>
        <li><b>Phone Number:</b> ${phone}</li>
        <li><b>Business Name:</b> ${businessName}</li>
        <li><b>Assigned Role:</b> ${role}</li>
      </ul>
      <p>We welcome ${firstname} ${lastname} to our platform and look forward to PatronWorks being a great companion to his business.</p>
      <p>Cheers,<br>Patronworks.net</p>`;

          sendMail(adminRecipients, adminSubject, `<h4>${adminMessage}</h4>`);

          // Welcome Email to User
          const userSubject = "Welcome to PatronWorks!";
          const userMessage = `
      <p>Hi ${firstname},</p>
      <p>Welcome to <strong>PatronWorks</strong>! 🎉</p>
      <p>We’re excited to have you on board. Here are a few resources to get you started:</p>
      <ul>
        <li><a href="https://patronworks.net/guides/getting-started" target="_blank">Getting Started Guide</a></li>
        <li><a href="https://patronworks.net/videos" target="_blank">Video Tutorials</a></li>
        <li><a href="https://patronworks.net/checklists/setup" target="_blank">Setup Checklist</a></li>
      </ul>
      <p>If you have any questions, feel free to reach out to our support team anytime.</p>
      <p>Thanks,<br>The PatronWorks Team</p>`;

          sendMail(email, userSubject, `<h4>${userMessage}</h4>`);

          return res.send({ message: "Account Verified: Thanks For Registering User", userId: savedUser._id });
        } else {
          return res.status(400).json({ error: "Cannot register user at the moment!" });
        }
      }

    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ message: "An error occurred" });
  }
})

export default router;