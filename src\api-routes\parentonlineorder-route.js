import express from 'express';
import { GetparentOnlineOrder,
    GetparentOnlineOrderById,
    PostparentOnlineOrder,
    updateparentOnlineOrder,
    deleteparentOnlineOrder
} from '../api/parentOnline-order.js'
const app=express.Router();

app.get('/online-order',GetparentOnlineOrder);
app.get('/online-order/:customerId',GetparentOnlineOrderById);
app.post('/online-order',PostparentOnlineOrder);
app.put('/online-order/:id',updateparentOnlineOrder);
app.delete('/online-order/:id',deleteparentOnlineOrder);

export default app ;
