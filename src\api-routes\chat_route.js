import express  from "express";
import { accessChat, allMessages, fetchCustomerChats, fetchsuperAdminChats, fetchAdminChats, sendMessage, deleteChat, deleteMessage } from "../api/chat_apis.js";
const routes=express.Router();



routes.put('/chat_access', accessChat )
routes.get('/fetch_Customer_chats', fetchCustomerChats )
routes.get('/fetch_Admin_chats', fetchAdminChats )
routes.get('/fetch_superAdmin_chat', fetchsuperAdminChats )
routes.delete('/delete_chats/:chatId', deleteChat )
routes.put('/send_message', sendMessage )
routes.delete('/delete_message/:messageId', deleteMessage )
routes.put('/all_messages/:chatId', allMessages )


export default routes