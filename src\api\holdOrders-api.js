// import mongoose from "mongoose";

import holdOrder from '../models/holdOrders.js';
export const getHoldOrders = async (req, res) => {
    try {
        const filter = {};

        if (req.query.userId) {
            filter.userId = { $in: req.query.userId.split(',') };
        }

        const data = await holdOrder
            .find(filter, { _id: 1, orderSummury: 1, employee: 1, table: 1 })
            .populate('employee', 'firstName lastName')
            .populate('table')
            .lean();

        const formattedData = data.map(order => {
            const orderSummary = order.orderSummury || {};

            return {
                _id: order._id,
                grandTotal: orderSummary.grandTotal ?? 0,
                product: orderSummary.product ?? [],
                timestamp: orderSummary.timestamp ?? null,
                orderNo: orderSummary.orderNo ?? '',
                customerName: orderSummary.customername ?? '',
                operator: order.employee
                    ? `${order.employee.firstName ?? ''} ${order.employee.lastName ?? ''}`.trim()
                    : 'Admin',
                table: order.table ?? {}
            };
        });

        res.json(formattedData);
    } catch (error) {
        console.error('Error in getHoldOrders:', error);
        res.status(500).json({ message: 'Server error', error });
    }
};


export const createHoldOrder = async (req, res) => {
  try {
    const { userId, orderSummury, employee, table } = req.body;

    if (!userId || !orderSummury) {
      return res.status(400).json({ message: '`userId` and `orderSummury` are required.' });
    }

    orderSummury.holdOrder = true;

    const newDoc = await holdOrder.create({ userId, orderSummury, employee, table });
    res.status(201).json(newDoc);
  } catch (err) {
    console.error('POST hold-orders:', err);
    res.status(500).json({ message: 'Server error', err });
  }
};

export const updateHoldOrder = async (req, res) => {
  try {
    const { id } = req.params;
    const updates  = req.body;

    const updated = await holdOrder.findByIdAndUpdate(
      id,
      { $set: updates },
      { new: true, runValidators: true }
    );

    if (!updated) {
      return res.status(404).json({ message: 'Hold order not found.' });
    }

    res.status(200).json(updated);
  } catch (err) {
    console.error('PATCH hold-orders:', err);
    res.status(500).json({ message: 'Server error', err });
  }
};

