import dotenv from 'dotenv'
import Stripe from 'stripe';
dotenv.config();
if (process.env.NODE_ENV === 'production') {
  var stripe = Stripe('***********************************************************************************************************');
} else if (process.env.NODE_ENV === 'development') {
  var stripe = Stripe('***********************************************************************************************************');
  // var stripe = Stripe('sk_test_51MiZTVF1YkHoz4Y5AsHfg9ovHa5zsRFHCfVrHSy5XKvxKtdKSMHpzQ5V0wEfcGHVfoEQ50NjXhCP0aF2aC1Mc05300eCAJlRxu');
}

export const terminalConnection = async (req, res) => {
  const { display_name, address, registration_code, label } = req.body;
  try {
    let location, reader, connectionToken;

    try {
      const allLocations = await stripe.terminal.locations.list({
        limit: 100
      });
      console.log('allLocations: ', allLocations);

      location = allLocations.data.find(
        (loc) => loc.display_name === display_name
      );
      console.log('location: ', location);

      if (!location) {
        location = await stripe.terminal.locations.create(
          {
            display_name,
            address,
          },
          // {
          //   stripeAccount,
          // }
        );
      } else {
        location = location;
      }
    } catch (error) {
      console.error('Error fetching/creating location:', error);
      if (error.type === 'StripeInvalidRequestError' && error.raw && error.raw.message) {
        res.status(400).json({ error: error.raw.message });
      } else {
        console.error(error);
        res.status(500).json({ error: 'An error occurred while fetching location' });
      }
      return;
    }

    try {
      const Allreader = await stripe.terminal.readers.list(
        {
          limit: 100,
          location: location.id
        },
      );
      console.log('Allreader: ', Allreader);
      // reader = Allreader.data.find(
      //   (loc) => loc.location === location.id
      // );
      // console.log('Reader: ', reader);

      if (Allreader) {
        reader = Allreader.data
      } else {
        reader = await stripe.terminal.readers.create(
          {
            registration_code,
            label,
            location: location.id,
          },
        );
      }
    } catch (error) {
      console.error('Error fetching/creating reader:', error);
      if (error.type === 'StripeInvalidRequestError' && error.raw && error.raw.message) {
        res.status(400).json({ error: error.raw.message });
      } else {
        console.error(error);
        res.status(500).json({ error: 'An error occurred while fetching reader' });
      }
      return;
    }
    try {
      connectionToken = await stripe.terminal.connectionTokens.create(
        {
          location: location.id,
        },
      );
    } catch (error) {
      console.error('Error creating connection token:', error);
      if (error.type === 'StripeInvalidRequestError' && error.raw && error.raw.message) {
        res.status(400).json({ error: error.raw.message });
      } else {
        console.error(error);
        res.status(500).json({ error: 'An error occurred during connection' });
      }
      return;
    }

    res.json({
      location,
      reader,
      connectionToken,
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    if (error.type === 'StripeInvalidRequestError' && error.raw && error.raw.message) {
      res.status(400).json({ error: error.raw.message });
    } else {
      console.error(error);
      res.status(500).json({ error: 'An error occurred' });
    }
  }
};

export const orderPaymentIntent = async (req, res) => {
  let paymentIntent;
  const { amount, currency, stripeAccount, application_fee_amount, readerId } = req.body
  try {
    paymentIntent = await stripe.paymentIntents.create(
      {
        amount,
        currency,
        payment_method_types: [
          'card_present'
        ],
        capture_method: 'automatic',
        application_fee_amount,
        transfer_data: {
          destination: stripeAccount,
        },
      }
    );
    const reader = await stripe.terminal.readers.processPaymentIntent(readerId, {
      payment_intent: paymentIntent.id
    })
    if (process.env.NODE_ENV === 'production') {
      res.json({ paymentIntent, reader })
    } else if (process.env.NODE_ENV === 'development') {
      const simulateReaderPayment = await stripe.testHelpers.terminal.readers.presentPaymentMethod(readerId)
      res.json({ paymentIntent, reader,  simulateReaderPayment })
      return;
    }
  } catch (error) {
    console.error(error);
    if (error.type === 'StripeInvalidRequestError' && error.raw && error.raw.message) {
      res.status(400).json({ error: error.raw.message });
    } else {
      console.error(error);
      res.status(500).json({ error: 'An error occurred' });
    }
  }
};

export const capturePaymentIntent = async (req, res) => {
  try {
    const capturePaymentIntent = await stripe.paymentIntents.capture(req.body.payment_intent_id);
    res.json(capturePaymentIntent);
  } catch (error) {
    if (error.type === 'StripeInvalidRequestError' && error.raw && error.raw.message) {
      res.status(400).json({ error: error.raw.message });
    } else {
      console.error(error);
      res.status(500).json({ error: 'An error occurred' });
    }
  }
};

export const confirmPaymentIntent = async (req, res) => {
  try {
    const paymentIntent = await stripe.paymentIntents.confirm(req.body.payment_intent_id);
    res.json(paymentIntent);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: error });
  }
};
