import express from 'express';
import {
  generateCustomerReferralCode,
  getCustomerReferralCode,
  generateUserReferralCode,
  getUserReferralCode,
  getCustomerbalance
} from '../api/referral.js';

const router = express.Router();

// Customer referral routes
router.post('/generate-customer-referral-code', generateCustomerReferralCode);
router.get('/check-customer-referral-status/:userId', getCustomerReferralCode);

// User referral routes
router.post('/generate-user-referral-code', generateUserReferralCode);
router.get('/check-user-referral-status/:userId', getUserReferralCode);

// customer Balance routes
router.get('/customer-referral-balance/:userId', getCustomerbalance);


export default router;
