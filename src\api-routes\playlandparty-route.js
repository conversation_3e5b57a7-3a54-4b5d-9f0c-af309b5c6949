import express  from "express";
import {awsupload} from "../middlewares/aws-s3-upload.js";
const routes=express.Router();

import {postParty , getParties , getPartyById, updateParty, deleteParty} from "../api/playlandparty.js"
import {getRoomById,getRooms,postRoom,updateRoom,deleteRoom} from "../api/payland-room.js"

routes.get('/party', getParties);
routes.get('/party/:id', getPartyById);
routes.post('/party', awsupload.single('party_pic'), postParty);
routes.put('/party/:id',awsupload.single('party_pic'), updateParty);
routes.delete('/party/:id', deleteParty);

///room api routes
routes.get('/room', getRooms);
routes.get('/room/:id', getRoomById);
routes.post('/room', awsupload.single('room_pic'), postRoom);
routes.put('/room/:id',awsupload.single('room_pic'), updateRoom);
routes.delete('/room/:id', deleteRoom);

export default routes