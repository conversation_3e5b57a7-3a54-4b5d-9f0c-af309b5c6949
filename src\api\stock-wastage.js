import wastageModel from '../models/stock-wastage.js';
import IngredientModel from '../models/ingredients.js';
import sendMail from '../middlewares/send-email.js';
import { User } from '../models/User.js'

export const getStockWastages = async (req, res) => {
    let filter = {}
    if (req.query.userId) {
        filter = { userId: req.query.userId.split(',') }
    }
    let data = await wastageModel.find(filter).populate('Supplier').populate('IngredientName');
    res.send(data);
}
export const getStockWastage = async (req, res) => {
    let data = await wastageModel.findOne(req.params).populate('Supplier').populate('IngredientName');
    res.send(data);
}

export const postStockWastage = async (req, res) => {
    const { IngredientName, userId, Quantity, ReasonOfWastage, PersonResponsible, Cost, Supplier, LocationOfWastage, DisposalPlan, PreventiveMeasure } = req.body;
    try {


        const user = await User.findOne({ _id: userId });
        const ingredient = await IngredientModel.findOne({ _id: IngredientName });



        if (ingredient && ingredient.stockHistory && ingredient.stockHistory.length > 0) {
            const sortedStockHistory = ingredient.stockHistory.sort((a, b) => {
                const dateA = new Date(a.expiry);
                const dateB = new Date(b.expiry);
                return dateA - dateB;
            });
            let remainingQuantity = req.body.Quantity;
            for (let i = 0; i < sortedStockHistory.length; i++) {
                const stockItem = sortedStockHistory[i];
                if (remainingQuantity <= 0) {
                    break;
                }
                if (stockItem.stock > 0) {
                    if (stockItem.stock >= remainingQuantity) {
                        stockItem.stock -= remainingQuantity;
                        remainingQuantity = 0;
                    } else {
                        remainingQuantity -= stockItem.stock;
                        stockItem.stock = 0;
                    }
                }
                const currentDate = new Date();
                if (stockItem.expiry < currentDate) {
                    stockItem.stock = 0;
                }
            }
            ingredient.stockHistory = sortedStockHistory.filter(stockItem => stockItem.stock > 0);


            let totalStock = 0;

            for (const stockItem of ingredient.stockHistory) {
                totalStock += stockItem.stock;
            }
            // Check if the total stock is less than the ThresholdLevel
            if (totalStock < ingredient.ThresholdLevel) {
                const email = user.email
                await sendMail(email, "Stock quantity are decrease ", `<html>

        <head>
        <style>
        
        .logo{
        width: 30%;
        }
        a{
        color:blue;
        cursor:pointer;
        text-decoration:none;}
        .maindivdata{
        padding:2rem 4rem;
        border: 1px solid lightgray;}
        
        .client{
         color: white;
         font-weight: 700;
         display: flex;
         font-size: 25px;
         width: 100%;
         justify-content:center;
         padding-top: 10rem;
         padding-left:20px;
        }
     
     .power{
     font-size:12px;
     color:gray;
     }
     p{
     font-size:16px;
         font-family: 'Poppins', sans-serif;
     
     }
    
     
        .container{
        width:50%;
        margin:auto;
            font-family: 'Poppins', sans-serif;
     
     
        
        }
        .infologo{
          background:transparent;
          border:none;
        }
        .shortimg{
          width:20px;
          height:20px;
          }
    
     h3{
            font-family: 'Poppins', sans-serif;
     
     }
     span{
            font-family: 'Poppins', sans-serif;
     
     }
     h5{
            font-family: 'Poppins', sans-serif;
     
     }
        @media screen and (max-width:900px) {
        .container{
        width:100%;
        margin:0px;
        
        }
        .client{
         color: white;
         font-weight: 700;
         display: grid;
         font-size: 25px;
         width: 100%;
         padding-top: 10rem;
         padding-left:10px;
        }
      .maindivdata{
        padding:2rem 10px;
        }
        .btn{
          font-size:12px
                  }
                  
        
        }
        
        </style>
          <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
     
        </head>
           <body>
           <div class="container">
           <div style="font-family: Arial, Helvetica, sans-serif; ">
           <div style="width: auto; height: 4rem;background-color: rgb(6, 138, 245); ">
           
               
           
           </div>
           <div class="maindivdata">
         <div class="top" style="  display:flex; 
         justify-content:center !important; align-items:center;"> 
     <img class="image" style=" justify-self:center ; margin-left:20%; display:flex; justify-content:center !important; align-items:center; width:60%;" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281194/WhatsApp_Image_2023-07-25_at_3.32.42_PM_gswsmd.jpg">
         </div>
                    <p style="font-size: 1rem; margin-bottom: 2rem;">Dear <strong>${email}</strong></p>
     
               <p style=" margin-bottom: 2rem;">We are excited to have you join us at PatronWorks.</p>
                         
               <p style=" margin-bottom: 2rem;">Please Add Stock on <strong>${ingredient.IngredientName}<strong> </p>
     
           
              
               <p style=" margin-bottom: 2rem;">We look forward to serving you.</p>
               <p style=" margin-bottom: 2rem;"> If you have any questions or require assistance, don't hesitate to contact our support team at <strong><EMAIL>. </strong></p>
               <p style=" margin-bottom: 2rem;">Thank you for choosing PatronWorks for your business!</p>
               
      
      <hr>
        
     <div style="display:flex; justify-content:space-between; margin-top:1rem;">
        <div>
        <img style="width:60%" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281267/WhatsApp_Image_2023-07-25_at_3.33.32_PM_xnwnuy.jpg">
        </div>
        <div style="display:flex; margin-left:45%;">
        <a style="margin-right:10px; href="https://www.linkedin.com/company/patronworks/">
          <img class="shortimg" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439086/WhatsApp_Image_2023-07-27_at_11.12.37_AM_1_whbn0t.jpg" alt="LinkedIn">
        </a>
        
        <a href="https://www.facebook.com/patronworks">
          <img class="shortimg" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439056/WhatsApp_Image_2023-07-27_at_11.12.37_AM_yedkyi.jpg" alt="Facebook">
        </a>
      </div>

        </div>
              
        
             </div>
        </div>
        </div>
        </body>
        </html>`)
                console.log(`Quantity is less in amount. Total stock: ${totalStock}, ThresholdLevel: ${ingredient.ThresholdLevel}`);
            }

            // Save the updated ingredient
            await ingredient.save();
        }


        const Data = new wastageModel({ IngredientName, userId, Quantity, ReasonOfWastage, PersonResponsible, Cost, Supplier, LocationOfWastage, DisposalPlan, PreventiveMeasure });
        const result = await Data.save();
        console.log(result, "StockWastage data saved to the database");
        const data = await result.populate('IngredientName')
        res.json(data);
    } catch (err) {
        res.status(400).send('Unable to save to the database');
        console.log(err);
    }
};


export const updateStockWastage = async (req, res) => {
    const user = await User.findOne({ _id: req.body.userId });
    const ingredient = await IngredientModel.findOne({ _id: req.body.IngredientName });
    if (ingredient && ingredient.stockHistory && ingredient.stockHistory.length > 0) {
        const sortedStockHistory = ingredient.stockHistory.sort((a, b) => {
            const dateA = new Date(a.expiry);
            const dateB = new Date(b.expiry);
            return dateA - dateB;
        });
        let remainingQuantity = req.body.Quantity;

        for (let i = 0; i < sortedStockHistory.length; i++) {
            const stockItem = sortedStockHistory[i];

            if (remainingQuantity <= 0) {
                break;
            }
            if (stockItem.stock > 0) {
                if (stockItem.stock >= remainingQuantity) {
                    stockItem.stock -= remainingQuantity;
                    remainingQuantity = 0;
                } else {
                    remainingQuantity -= stockItem.stock;
                    stockItem.stock = 0;
                }
            }
            const currentDate = new Date();
            if (stockItem.expiry < currentDate) {
                stockItem.stock = 0;
            }
        }
        ingredient.stockHistory = sortedStockHistory.filter(stockItem => stockItem.stock > 0);

        let totalStock = 0;

        for (const stockItem of ingredient.stockHistory) {
            totalStock += stockItem.stock;
        }
        console.log("totalStock", totalStock, "ingredient.ThresholdLevel :::: ", ingredient.ThresholdLevel)
        // Check if the total stock is less than the ThresholdLevel
        if (totalStock < ingredient.ThresholdLevel) {
            const email = user.email
            await sendMail(email, "Stock quantity are decrease ", `<html>

        <head>
        <style>
        
        .logo{
        width: 30%;
        }
        a{
        color:blue;
        cursor:pointer;
        text-decoration:none;}
        .maindivdata{
        padding:2rem 4rem;
        border: 1px solid lightgray;}
        
        .client{
         color: white;
         font-weight: 700;
         display: flex;
         font-size: 25px;
         width: 100%;
         justify-content:center;
         padding-top: 10rem;
         padding-left:20px;
        }
     
     .power{
     font-size:12px;
     color:gray;
     }
     p{
     font-size:16px;
         font-family: 'Poppins', sans-serif;
     
     }
    
     
        .container{
        width:50%;
        margin:auto;
            font-family: 'Poppins', sans-serif;
     
     
        
        }
        .infologo{
          background:transparent;
          border:none;
        }
        .shortimg{
          width:20px;
          height:20px;
          }
    
     h3{
            font-family: 'Poppins', sans-serif;
     
     }
     span{
            font-family: 'Poppins', sans-serif;
     
     }
     h5{
            font-family: 'Poppins', sans-serif;
     
     }
        @media screen and (max-width:900px) {
        .container{
        width:100%;
        margin:0px;
        
        }
        .client{
         color: white;
         font-weight: 700;
         display: grid;
         font-size: 25px;
         width: 100%;
         padding-top: 10rem;
         padding-left:10px;
        }
      .maindivdata{
        padding:2rem 10px;
        }
        .btn{
          font-size:12px
                  }
                  
        
        }
        
        </style>
          <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
     
        </head>
           <body>
           <div class="container">
           <div style="font-family: Arial, Helvetica, sans-serif; ">
           <div style="width: auto; height: 4rem;background-color: rgb(6, 138, 245); ">
           
               
           
           </div>
           <div class="maindivdata">
         <div class="top" style="  display:flex; 
         justify-content:center !important; align-items:center;"> 
     <img class="image" style=" justify-self:center ; margin-left:20%; display:flex; justify-content:center !important; align-items:center; width:60%;" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281194/WhatsApp_Image_2023-07-25_at_3.32.42_PM_gswsmd.jpg">
         </div>
                    <p style="font-size: 1rem; margin-bottom: 2rem;">Dear <strong>${email}</strong></p>
     
               <p style=" margin-bottom: 2rem;">We are excited to have you join us at PatronWorks.</p>
                         
               <p style=" margin-bottom: 2rem;">Please Add Stock on <strong>${ingredient.IngredientName}<strong> </p>
     
           
              
               <p style=" margin-bottom: 2rem;">We look forward to serving you.</p>
               <p style=" margin-bottom: 2rem;"> If you have any questions or require assistance, don't hesitate to contact our support team at <strong><EMAIL>. </strong></p>
               <p style=" margin-bottom: 2rem;">Thank you for choosing PatronWorks for your business!</p>
               
      
      <hr>
        
     <div style="display:flex; justify-content:space-between; margin-top:1rem;">
        <div>
        <img style="width:60%" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281267/WhatsApp_Image_2023-07-25_at_3.33.32_PM_xnwnuy.jpg">
        </div>
        <div style="display:flex; margin-left:45%;">
        <a style="margin-right:10px; href="https://www.linkedin.com/company/patronworks/">
          <img class="shortimg" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439086/WhatsApp_Image_2023-07-27_at_11.12.37_AM_1_whbn0t.jpg" alt="LinkedIn">
        </a>
        
        <a href="https://www.facebook.com/patronworks">
          <img class="shortimg" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439056/WhatsApp_Image_2023-07-27_at_11.12.37_AM_yedkyi.jpg" alt="Facebook">
        </a>
      </div>

        </div>
              
        
             </div>
        </div>
        </div>
        </body>
        </html>`)
            console.log(`Quantity is less in amount. Total stock: ${totalStock}, ThresholdLevel: ${ingredient.ThresholdLevel}`);
        }

        // Save the updated ingredient
        await ingredient.save();
    }
    let data = await wastageModel.findByIdAndUpdate(
        { _id: req.body._id },
        {
            $set: req.body
        },{new: true}).populate('IngredientName');
    if (data) {
        res.send({ data, message: "StockWastage data updated successfully" });
    }
    else {
        res.send({ message: "StockWastage data cannot be updated successfully" })
    }
}
export const deleteStockWastage = async (req, res) => {
    console.log(req.params)
    let data = await wastageModel.deleteOne(req.params)
    if (data) {
        res.send({ message: "StockWastage data delete successfully" });
    }
    else {
        res.send({ message: "StockWastage data cannot delete successfully" })
    }
}