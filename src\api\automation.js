import fs from "fs";
import fse from "fs-extra";
import path from "path";
import { fileURLToPath } from 'url';
import axios from "axios";
import simpleGit from "simple-git";
import  {User}  from "../models/User.js";
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration constants - Use environment variables for flexibility
const TEMPLATE_REPO = process.env.TEMPLATE_REPO || "https://github.com/saudkhanbpk/inihabesha.git";
const PROJECTS_DIR = path.join(__dirname, "projects");
const ANGULAR_PROJECT_NAME = "IniHabesha";
const GITHUB_USERNAME = process.env.GITHUB_USERNAME || "saudkhanbpk";

// Fallback template repositories to try if primary fails
const FALLBACK_TEMPLATES = [
    "https://github.com/saudkhanbpk/inihabesha.git",
    "https://github.com/saudkhanbpk/teststore-store.git",
    "https://github.com/saudkhanbpk/store-template.git"
];

// --- SECURITY WARNING ---
// These should be moved to environment variables in production
const GITHUB_TOKEN = '*********************************************************************************************';
const NETLIFY_AUTH_TOKEN = '****************************************';
const NETLIFY_GITHUB_INSTALLATION_ID = 27811724;

// Create projects directory if it doesn't exist
// if (!fs.existsSync(PROJECTS_DIR)) {
//     fs.mkdirSync(PROJECTS_DIR, { recursive: true });
// }
if (!fs.existsSync(PROJECTS_DIR)) fs.mkdirSync(PROJECTS_DIR);

// Helper function to create authenticated GitHub URL
function getAuthenticatedGitHubUrl(repoName) {
    return `https://${GITHUB_TOKEN}@github.com/${GITHUB_USERNAME}/${repoName}.git`;
}

// Function to get GitHub repository ID
async function getGitHubRepoId(repoName) {
    try {
        const response = await axios.get(`https://api.github.com/repos/${GITHUB_USERNAME}/${repoName}`, {
            headers: {
                'Authorization': `token ${GITHUB_TOKEN}`,
                'User-Agent': 'Store-generator'
            }
        });
        return response.data.id;
    } catch (error) {
        console.error('❌ Failed to get GitHub repo ID:', error.response?.data || error.message);
        throw error;
    }
}

// Function to get Netlify GitHub installation ID
async function getNetlifyGitHubInstallationId() {
    if (NETLIFY_GITHUB_INSTALLATION_ID) {
        return NETLIFY_GITHUB_INSTALLATION_ID;
    }
    try {
        const response = await axios.get('https://api.netlify.com/api/v1/accounts', {
            headers: {
                'Authorization': `Bearer ${NETLIFY_AUTH_TOKEN}`,
                'Content-Type': 'application/json'
            }
        });
        const account = response.data[0];
        if (account && account.github_installation_id) {
            console.log(`✅ Found GitHub installation ID: ${account.github_installation_id}`);
            return account.github_installation_id;
        }
        throw new Error('No GitHub installation ID found in account');
    } catch (error) {
        console.error('❌ Could not get GitHub installation ID automatically');
        console.error('Please follow these steps:');
        console.error('1. Go to https://github.com/settings/installations');
        console.error('2. Find "Netlify" in the list and click "Configure"');
        console.error('3. Copy the number at the end of the URL (e.g., if URL is github.com/settings/installations/12345, use 12345)');
        console.error('4. Set NETLIFY_GITHUB_INSTALLATION_ID = 12345 in your environment variables');
        throw new Error('GitHub installation ID required for GitHub integration');
    }
}

// Function to deploy to Netlify with GitHub integration
async function deployToNetlifyWithGitHub(username, repoName) {
    try {
        console.log(`🔍 Getting GitHub repository information...`);
        const repoId = await getGitHubRepoId(repoName);
        console.log(`✅ GitHub repo ID: ${repoId}`);

        const installationId = await getNetlifyGitHubInstallationId();
        console.log(`✅ Netlify installation ID: ${installationId}`);

        const repoResponse = await axios.get(`https://api.github.com/repos/${GITHUB_USERNAME}/${repoName}`, {
            headers: {
                'Authorization': `token ${GITHUB_TOKEN}`,
                'User-Agent': 'Store-generator',
                'Accept': 'application/vnd.github.v3+json'
            }
        });
        const isPrivate = repoResponse.data.private;

        const sitePayload = {
            name: `${username}-store-${Date.now()}`,
            repo: {
                provider: 'github',
                id: repoId,
                repo: `${GITHUB_USERNAME}/${repoName}`,
                private: isPrivate,
                branch: "main",
                installation_id: installationId
            },
            build_settings: {
                cmd: "npm run build",
                dir: `dist/${ANGULAR_PROJECT_NAME}`,
                repo_type: "git"
            }
        };

        console.log(`🌐 Creating Netlify site with GitHub connection...`);
        console.log(`📋 Site payload:`, JSON.stringify(sitePayload, null, 2));

        const siteResponse = await axios.post(
            'https://api.netlify.com/api/v1/sites',
            sitePayload,
            {
                headers: {
                    Authorization: `Bearer ${NETLIFY_AUTH_TOKEN}`,
                    "Content-Type": "application/json"
                }
            }
        );

        const deployedUrl = siteResponse.data.url;
        console.log(`🎉 Site created and connected to GitHub: ${deployedUrl}`);
        console.log(`🔄 First deployment should start automatically...`);

        return {
            success: true,
            deployedUrl,
            siteId: siteResponse.data.id,
            netlifySubdomain: siteResponse.data.name + '.netlify.app'
        };
    } catch (error) {
        console.error("❌ Netlify GitHub deployment failed:");
        if (error.response) {
            console.error("Status:", error.response.status);
            console.error("Data:", JSON.stringify(error.response.data, null, 2));
        } else {
            console.error("Error:", error.message);
        }
        throw error;
    }
}

// Function to set up Git repository
async function setupGitRepository(userFolder, repoCloneUrl, username) {
    const userGit = simpleGit(userFolder);
    try {
        console.log(`🔧 Setting up Git repository...`);
        await userGit.init();
        await userGit.addConfig('user.name', 'Store Generator');
        await userGit.addConfig('user.email', '<EMAIL>');

        try {
            await userGit.removeRemote("origin");
        } catch (error) {
            if (!error.message.includes('remote origin does not exist')) {
                throw error;
            }
        }

        await userGit.addRemote("origin", repoCloneUrl);

        try {
            await userGit.checkoutLocalBranch("main");
        } catch (err) {
            if (err.message.includes('already exists')) {
                await userGit.checkout("main");
            } else {
                await userGit.checkout(['-b', 'main']);
            }
        }

        await userGit.add(".");
        const status = await userGit.status();
        console.log(`📋 Git status:`, status.files.length, 'files to commit');

        if (status.files.length > 0) {
            await userGit.commit(`Initial commit for ${username} store`);
            console.log(`✅ Changes committed successfully`);
        }

        console.log(`⬆️ Pushing to GitHub...`);
        await userGit.push("origin", "main", ["--force", "--set-upstream"]);
        console.log(`✅ Successfully pushed to GitHub repository`);

        try {
            const remoteResponse = await axios.get(`https://api.github.com/repos/${GITHUB_USERNAME}/${username}-store/branches/main`, {
                headers: {
                    'Authorization': `token ${GITHUB_TOKEN}`,
                    'User-Agent': 'store-generator',
                    'Accept': 'application/vnd.github.v3+json'
                }
            });
            console.log(`✅ Main branch verified on GitHub:`, remoteResponse.data.name);
        } catch (verifyError) {
            console.warn(`⚠️ Could not verify main branch:`, verifyError.response?.status || verifyError.message);
        }

        return true;
    } catch (error) {
        console.error("❌ Git setup failed:", error);
        throw error;
    }
}

// Function to fetch file content from GitHub
async function fetchFileContentFromGitHub(repoName, filePath) {
    try {
        const response = await axios.get(
            `https://api.github.com/repos/${GITHUB_USERNAME}/${repoName}/contents/${filePath}`,
            {
                headers: {
                    'Authorization': `token ${GITHUB_TOKEN}`,
                    'User-Agent': 'store-generator',
                    'Accept': 'application/vnd.github.v3.raw'
                }
            }
        );
        return response.data;
    } catch (error) {
        console.error(`❌ Failed to fetch file content from ${filePath}:`, error.response?.data || error.message);
        throw error;
    }
}

// Normalize string: lowercase + remove all spaces
function normalize(str) {
    return str.toLowerCase().replace(/\s+/g, '');
}

// Helper: Recursively replace value in nested object
function deepReplaceValue(obj, searchStr, replaceStr) {
    const normalizedSearch = normalize(searchStr);
    if (typeof obj === 'string') {
        const normalizedObj = normalize(obj);
        if (normalizedObj === normalizedSearch) {
            console.log(`Replacing: "${obj}" => "${replaceStr}"`);
            return replaceStr;
        }
        return obj;
    } else if (Array.isArray(obj)) {
        return obj.map(item => deepReplaceValue(item, searchStr, replaceStr));
    } else if (typeof obj === 'object' && obj !== null) {
        const newObj = {};
        for (const key in obj) {
            newObj[key] = deepReplaceValue(obj[key], searchStr, replaceStr);
        }
        return newObj;
    }

    return obj;
}

async function updateRepositoryFiles(userFolder, files, repoName) {
    try {
        console.log(`📝 Updating files in repository...`);
        for (const file of files) {
            const { filePath, content, replace } = file;
            const fullPath = path.join(userFolder, filePath);

            if (!filePath || typeof filePath !== 'string') {
                throw new Error(`Invalid or missing filePath in file: ${JSON.stringify(file)}`);
            }

            // Ensure directory exists
            const dir = path.dirname(fullPath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }

            if (replace) {
                if (!replace.search || !replace.replace) {
                    throw new Error(`Invalid replace object in file: ${JSON.stringify(file)}`);
                }

                const fileRaw = fs.readFileSync(fullPath, 'utf-8');
                const json = JSON.parse(fileRaw);

                const updatedJson = deepReplaceValue(json, replace.search.trim(), replace.replace.trim());

                fs.writeFileSync(fullPath, JSON.stringify(updatedJson, null, 2), 'utf-8');
                console.log(`✅ Replaced "${replace.search}" with "${replace.replace}" in ${filePath}`);
            } else if (content) {
                fs.writeFileSync(fullPath, content, 'utf-8');
                console.log(`✅ Overwritten ${filePath}`);
            } else {
                throw new Error(`No content or replace object provided for file: ${filePath}`);
            }
        }
        return true;
    } catch (error) {
        console.error(`❌ Failed to update files:`, error.message);
        throw error;
    }
}

// Function to commit and push changes
async function commitAndPushChanges(userFolder, username, commitMessage) {
    const userGit = simpleGit(userFolder);
    try {
        console.log(`🔧 Committing and pushing changes...`);

        // Ensure remote is properly configured with authentication
        const repoName = `${username}-store`;
        const authenticatedUrl = getAuthenticatedGitHubUrl(repoName);

        try {
            await userGit.removeRemote('origin');
        } catch (error) {
            // Remote might not exist, that's okay
        }
        await userGit.addRemote('origin', authenticatedUrl);

        await userGit.add(".");
        const status = await userGit.status();
        console.log(`📋 Git status:`, status.files.length, 'files to commit');

        if (status.files.length > 0) {
            await userGit.commit(commitMessage || `Update Store for ${username}`);
            console.log(`✅ Changes committed successfully`);

            console.log(`⬆️ Pushing to GitHub...`);
            await userGit.push("origin", "main");
            console.log(`✅ Successfully pushed changes to GitHub repository`);
        } else {
            console.log(`ℹ️ No changes to commit`);
        }

        return true;
    } catch (error) {
        console.error(`❌ Failed to commit and push changes:`, error.message);
        throw error;
    }
}

// Function to add a custom domain to a Netlify site via API
async function addCustomDomainToNetlifySite(siteId, domainName) {
    try {
        console.log(`🔗 Adding custom domain '${domainName}' to Netlify site '${siteId}'...`);
        const response = await axios.patch(
            `https://api.netlify.com/api/v1/sites/${siteId}`,
            {
                custom_domain: domainName
            },
            {
                headers: {
                    Authorization: `Bearer ${NETLIFY_AUTH_TOKEN}`,
                    "Content-Type": "application/json"
                }
            }
        );
        console.log(`✅ Custom domain '${domainName}' added to Netlify site. Response:`, response.data.custom_domain);
        return true;
    } catch (error) {
        console.error(`❌ Failed to add custom domain to Netlify site:`);
        if (error.response) {
            console.error("Status:", error.response.status);
            console.error("Data:", JSON.stringify(error.response.data, null, 2));
        } else {
            console.error("Error:", error.message);
        }
        throw error;
    }
}

// Function to verify GitHub token permissions
async function verifyGitHubToken() {
    try {
        console.log(`🔑 Verifying GitHub token permissions...`);
        const response = await axios.get('https://api.github.com/user', {
            headers: {
                Authorization: `token ${GITHUB_TOKEN}`,
                "User-Agent": "store-generator",
                Accept: "application/vnd.github.v3+json"
            }
        });

        console.log(`✅ Token valid for user: ${response.data.login}`);

        // Check token scopes
        const scopes = response.headers['x-oauth-scopes'] || '';
        console.log(`🔐 Token scopes: ${scopes}`);

        return {
            valid: true,
            user: response.data.login,
            scopes: scopes.split(',').map(s => s.trim())
        };
    } catch (error) {
        console.error(`❌ GitHub token verification failed:`, error.response?.data || error.message);
        return {
            valid: false,
            error: error.response?.data || error.message
        };
    }
}

// Function to list available repositories for debugging
async function listAvailableRepositories() {
    try {
        console.log(`📋 Listing repositories for user: ${GITHUB_USERNAME}`);
        const response = await axios.get(`https://api.github.com/users/${GITHUB_USERNAME}/repos`, {
            headers: {
                Authorization: `token ${GITHUB_TOKEN}`,
                "User-Agent": "store-generator",
                Accept: "application/vnd.github.v3+json"
            },
            params: {
                per_page: 100,
                sort: 'updated'
            }
        });

        const repos = response.data.map(repo => ({
            name: repo.name,
            full_name: repo.full_name,
            is_template: repo.is_template,
            private: repo.private,
            clone_url: repo.clone_url
        }));

        console.log(`✅ Found ${repos.length} repositories:`);
        repos.forEach(repo => {
            console.log(`  - ${repo.name} ${repo.is_template ? '(TEMPLATE)' : ''} ${repo.private ? '(PRIVATE)' : '(PUBLIC)'}`);
        });

        return repos;
    } catch (error) {
        console.error(`❌ Failed to list repositories:`, error.response?.data || error.message);
        return [];
    }
}

// Function to verify if template repository exists and is a template
async function verifyTemplateRepository(templateRepoName) {
    try {
        console.log(`🔍 Verifying template repository: ${templateRepoName}`);
        const response = await axios.get(
            `https://api.github.com/repos/${GITHUB_USERNAME}/${templateRepoName}`,
            {
                headers: {
                    Authorization: `token ${GITHUB_TOKEN}`,
                    "User-Agent": "store-generator",
                    Accept: "application/vnd.github.v3+json"
                }
            }
        );

        const repo = response.data;
        console.log(`✅ Repository found: ${repo.full_name}`);
        console.log(`📋 Is template: ${repo.is_template}`);
        console.log(`🔒 Is private: ${repo.private}`);

        return {
            exists: true,
            isTemplate: repo.is_template,
            isPrivate: repo.private,
            repoData: repo
        };
    } catch (error) {
        console.error(`❌ Template repository verification failed:`, error.response?.data || error.message);

        // If repository not found, list available repositories for debugging
        if (error.response?.status === 404) {
            console.log(`🔍 Repository '${templateRepoName}' not found. Listing available repositories...`);
            const availableRepos = await listAvailableRepositories();

            // Look for similar repository names
            const similarRepos = availableRepos.filter(repo =>
                repo.name.toLowerCase().includes('store') ||
                repo.name.toLowerCase().includes('template') ||
                repo.name.toLowerCase().includes('test')
            );

            if (similarRepos.length > 0) {
                console.log(`💡 Found similar repositories that might work as templates:`);
                similarRepos.forEach(repo => {
                    console.log(`  - ${repo.name} ${repo.is_template ? '(TEMPLATE)' : ''} ${repo.private ? '(PRIVATE)' : '(PUBLIC)'}`);
                });
            }
        }

        return {
            exists: false,
            isTemplate: false,
            error: error.response?.data || error.message
        };
    }
}

// Robust function to create repository with fallback templates
async function createRepositoryFromTemplate(templateRepo, newRepoName, username) {
    const templatesToTry = [templateRepo, ...FALLBACK_TEMPLATES.filter(t => t !== templateRepo)];

    for (let i = 0; i < templatesToTry.length; i++) {
        const currentTemplate = templatesToTry[i];
        const templateRepoName = currentTemplate.split('/').pop().replace('.git', '');

        try {
            console.log(`� Attempt ${i + 1}: Creating repository ${newRepoName} from template ${templateRepoName}...`);

            // First verify the template repository
            const verification = await verifyTemplateRepository(templateRepoName);

            if (!verification.exists) {
                console.warn(`⚠️ Template '${templateRepoName}' not found, trying next...`);
                continue;
            }

            // Try to create from template or fork
            let response;
            if (verification.isTemplate) {
                console.log(`🚀 Creating from template: ${GITHUB_USERNAME}/${templateRepoName}`);
                response = await axios.post(
                    `https://api.github.com/repos/${GITHUB_USERNAME}/${templateRepoName}/generate`,
                    {
                        owner: GITHUB_USERNAME,
                        name: newRepoName,
                        description: `Auto-generated store for ${username}`,
                        include_all_branches: false,
                        private: false
                    },
                    {
                        headers: {
                            Authorization: `token ${GITHUB_TOKEN}`,
                            "User-Agent": "store-generator",
                            Accept: "application/vnd.github.v3+json"
                        }
                    }
                );
            } else {
                console.log(`🍴 Repository is not a template, forking instead: ${templateRepoName}`);
                response = await forkRepository(templateRepoName, newRepoName, username);
            }

            console.log(`✅ Repository ${newRepoName} created successfully from ${templateRepoName}`);
            return response.data || response;

        } catch (error) {
            if (error.response?.status === 422) {
                console.warn("⚠️ Repository already exists, proceeding...");
                return { name: newRepoName, clone_url: `https://github.com/${GITHUB_USERNAME}/${newRepoName}.git` };
            }

            console.warn(`❌ Failed with template ${templateRepoName}:`, error.response?.data?.message || error.message);

            // If this is the last template, throw the error
            if (i === templatesToTry.length - 1) {
                throw new Error(`Failed to create repository from any available template. Last error: ${error.message}`);
            }
        }
    }

    throw new Error('No valid template repositories found');
}

// Alternative function to fork repository if template is not available
async function forkRepository(templateRepoName, newRepoName, username) {
    try {
        console.log(`🍴 Forking repository ${templateRepoName} as ${newRepoName}...`);

        // First fork the repository
        const forkResponse = await axios.post(
            `https://api.github.com/repos/${GITHUB_USERNAME}/${templateRepoName}/forks`,
            {
                name: newRepoName,
                default_branch_only: true
            },
            {
                headers: {
                    Authorization: `token ${GITHUB_TOKEN}`,
                    "User-Agent": "store-generator",
                    Accept: "application/vnd.github.v3+json"
                }
            }
        );

        console.log(`✅ Repository forked successfully`);

        // Wait a moment for GitHub to process the fork
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Rename the forked repository if needed
        if (forkResponse.data.name !== newRepoName) {
            console.log(`🔄 Renaming forked repository to ${newRepoName}...`);
            const renameResponse = await axios.patch(
                `https://api.github.com/repos/${GITHUB_USERNAME}/${forkResponse.data.name}`,
                {
                    name: newRepoName,
                    description: `Auto-generated store for ${username}`
                },
                {
                    headers: {
                        Authorization: `token ${GITHUB_TOKEN}`,
                        "User-Agent": "store-generator",
                        Accept: "application/vnd.github.v3+json"
                    }
                }
            );
            return renameResponse.data;
        }

        return forkResponse.data;

    } catch (error) {
        console.error("❌ Failed to fork repository:", error.response?.data || error.message);
        throw error;
    }
}
export const generateStore = async (req, res) => {
    const { userId, username } = req.body;

    if (!userId || !username) {
        return res.status(400).json({ message: "userId and username are required fields" });
    }

    const repoName = `${username}-store`;

    try {
        console.log(`🚀 Starting store generation for ${username}...`);
        console.log(`📋 Environment: ${process.env.NODE_ENV || 'development'}`);

        // Verify GitHub token first
        const tokenVerification = await verifyGitHubToken();
        if (!tokenVerification.valid) {
            throw new Error(`GitHub token verification failed: ${tokenVerification.error}`);
        }
        console.log(`✅ GitHub token verified for user: ${tokenVerification.user}`);

        // Create repository from template with fallback options
        console.log(`📁 Creating repository ${repoName}...`);
        const newRepo = await createRepositoryFromTemplate(TEMPLATE_REPO, repoName, username);
        console.log(`✅ Repository created: ${newRepo.name}`);

        // Wait for GitHub to fully process the new repository
        console.log('⏳ Waiting for GitHub to process the repository...');
        await new Promise(resolve => setTimeout(resolve, 8000));

        // Deploy to Netlify with GitHub integration
        console.log('🌐 Deploying to Netlify...');
        const deployResult = await deployToNetlifyWithGitHub(username, repoName);

        // Update user in database
        try {
            await User.updateOne(
                { _id: userId },
                {
                    $set: {
                        storeName:username,
                        githubRepo: repoName,
                        netlifyUrl: deployResult.deployedUrl
                    }
                }
            );
            console.log("✅ User updated with GitHub and Netlify info.");
        } catch (dbErr) {
            console.error("❌ DB update failed:", dbErr.message);
        }

        res.status(200).json({
            message: "✅ Store created successfully via GitHub template!",
            storeName:username,
            deployedUrl: deployResult.deployedUrl,
            githubUrl: `https://github.com/${GITHUB_USERNAME}/${repoName}`,
            deploymentType: "github-template",
            note: "Repository created directly from template without local operations"
        });

    } catch (err) {
        console.error("❌ Error:", err);
        if (!res.headersSent) {
            res.status(500).json({
                message: "Something went wrong",
                error: err.message || err.toString()
            });
        }
    }
};


// Function to update files directly on GitHub using GitHub API
async function updateFileOnGitHub(repoName, filePath, content, commitMessage) {
    try {
        console.log(`📝 Updating file ${filePath} on GitHub...`);

        // First, get the current file to get its SHA (required for updates)
        let fileSha = null;
        try {
            const currentFileResponse = await axios.get(
                `https://api.github.com/repos/${GITHUB_USERNAME}/${repoName}/contents/${filePath}`,
                {
                    headers: {
                        Authorization: `token ${GITHUB_TOKEN}`,
                        "User-Agent": "store-generator",
                        Accept: "application/vnd.github.v3+json"
                    }
                }
            );
            fileSha = currentFileResponse.data.sha;
            console.log(`✅ Found existing file, SHA: ${fileSha}`);
        } catch (error) {
            if (error.response?.status === 404) {
                console.log(`📄 File ${filePath} doesn't exist, will create new file`);
            } else {
                throw error;
            }
        }

        // Prepare the update/create request
        const updateData = {
            message: commitMessage || `Update ${filePath}`,
            content: Buffer.from(content).toString('base64'),
            committer: {
                name: 'saudkhanbpk',
                email: '<EMAIL>'
            }
        };

        // Add SHA if file exists (for updates)
        if (fileSha) {
            updateData.sha = fileSha;
        }

        // Update or create the file
        const response = await axios.put(
            `https://api.github.com/repos/${GITHUB_USERNAME}/${repoName}/contents/${filePath}`,
            updateData,
            {
                headers: {
                    Authorization: `token ${GITHUB_TOKEN}`,
                    "User-Agent": "store-generator",
                    Accept: "application/vnd.github.v3+json"
                }
            }
        );

        console.log(`✅ File ${filePath} updated successfully on GitHub`);
        return response.data;
    } catch (error) {
        console.error(`❌ Failed to update file ${filePath}:`, error.response?.data || error.message);
        throw error;
    }
}

// Production-ready updateStore function with fallback mechanisms
export const updateStore = async (req, res) => {
    console.log('Received /update request:', JSON.stringify(req.body, null, 2));

    if (!req.body) {
        return res.status(400).json({ message: "Request body is missing" });
    }

    const { username, files, commitMessage } = req.body;

    if (typeof username !== 'string' || !username.trim()) {
        return res.status(400).json({ message: "Username must be a non-empty string" });
    }

    if (!files || !Array.isArray(files) || files.length === 0) {
        return res.status(400).json({ message: "Files array is required and must not be empty" });
    }

    const repoName = `${username}-store`;
    const userFolder = path.join(PROJECTS_DIR, username);

    // Determine if we should use GitHub API or local git operations
    const useGitHubAPI = process.env.NODE_ENV === 'production' || process.env.USE_GITHUB_API === 'true';

    try {
        console.log(`🔄 Starting store update for ${username}...`);
        console.log(`📋 Update method: ${useGitHubAPI ? 'GitHub API' : 'Local Git'}`);

        if (useGitHubAPI) {
            // Use GitHub API for production (more reliable)
            return await updateStoreViaGitHubAPI(req, res, username, files, commitMessage, repoName);
        } else {
            // Use local git operations for development
            return await updateStoreViaLocalGit(req, res, username, files, commitMessage, repoName, userFolder);
        }

    } catch (err) {
        console.error("❌ Update Error:", err);

        // If local git fails, try GitHub API as fallback
        if (!useGitHubAPI && !res.headersSent) {
            console.log("🔄 Local git failed, trying GitHub API as fallback...");
            try {
                return await updateStoreViaGitHubAPI(req, res, username, files, commitMessage, repoName);
            } catch (fallbackErr) {
                console.error("❌ Fallback also failed:", fallbackErr);
            }
        }

        // Clean up in case of error
        if (fs.existsSync(userFolder)) {
            try {
                fse.removeSync(userFolder);
            } catch (cleanupErr) {
                console.warn("⚠️ Failed to cleanup folder:", cleanupErr.message);
            }
        }

        if (!res.headersSent) {
            res.status(500).json({
                message: "Something went wrong during update",
                error: err.message || err.toString(),
                method: useGitHubAPI ? 'GitHub API' : 'Local Git'
            });
        }
    }
};
// GitHub API method (production-ready)
async function updateStoreViaGitHubAPI(req, res, username, files, commitMessage, repoName) {
    console.log(`🌐 Using GitHub API method for ${username}...`);

    const updatedFiles = [];
    const skippedFiles = [];

    for (const file of files) {
        const { filePath, content, replace } = file;

        if (!filePath || typeof filePath !== 'string') {
            throw new Error(`Invalid or missing filePath in file: ${JSON.stringify(file)}`);
        }

        let finalContent = content;

        // Handle replace operations
        if (replace) {
            if (!replace.search || !replace.replace) {
                throw new Error(`Invalid replace object in file: ${JSON.stringify(file)}`);
            }

            try {
                // Get current file content from GitHub
                const currentContent = await fetchFileContentFromGitHub(repoName, filePath);
                const json = JSON.parse(currentContent);

                // Apply the replacement
                const updatedJson = deepReplaceValue(json, replace.search.trim(), replace.replace.trim());
                finalContent = JSON.stringify(updatedJson, null, 2);

                console.log(`🔄 Applied replacement: "${replace.search}" → "${replace.replace}" in ${filePath}`);
            } catch (error) {
                if (error.response?.status === 404) {
                    console.warn(`⚠️ File ${filePath} not found for replacement operation.`);
                    if (content) {
                        console.log(`📄 Using provided content as fallback for ${filePath}`);
                        finalContent = content;
                    } else {
                        console.log(`💡 Skipping ${filePath} - file not found and no fallback content provided.`);
                        skippedFiles.push({
                            filePath,
                            reason: 'File not found for replacement operation and no fallback content provided'
                        });
                        continue;
                    }
                } else {
                    console.error(`❌ Failed to apply replacement in ${filePath}:`, error.message);
                    throw error;
                }
            }
        } else if (!content) {
            throw new Error(`No content or replace object provided for file: ${filePath}`);
        }

        // Update the file directly on GitHub
        await updateFileOnGitHub(
            repoName,
            filePath,
            finalContent,
            commitMessage || `Update ${filePath} for ${username} store`
        );

        updatedFiles.push(filePath);
    }

    const responseMessage = skippedFiles.length > 0
        ? `✅ Store update completed! ${updatedFiles.length} files updated, ${skippedFiles.length} files skipped.`
        : "✅ Store updated successfully via GitHub API!";

    const response = {
        message: responseMessage,
        githubUrl: `https://github.com/${GITHUB_USERNAME}/${repoName}`,
        updatedFiles: updatedFiles,
        updateMethod: "github-api",
        note: "Files updated directly on GitHub - Netlify will automatically redeploy"
    };

    if (skippedFiles.length > 0) {
        response.skippedFiles = skippedFiles;
        response.warning = "Some files were skipped. Check skippedFiles for details.";
    }

    res.status(200).json(response);
}

// Local Git method (development/fallback)
async function updateStoreViaLocalGit(req, res, username, files, commitMessage, repoName, userFolder) {
    console.log(`💻 Using Local Git method for ${username}...`);

    const git = simpleGit();
    const repoCloneUrl = getAuthenticatedGitHubUrl(repoName);

    // Clean up existing folder
    if (fs.existsSync(userFolder)) {
        console.log(`🧹 Cleaning up existing folder: ${userFolder}`);
        fse.removeSync(userFolder);
    }

    // Clone the user's store repository
    console.log(`📁 Cloning store repository: ${repoName}...`);
    await git.clone(repoCloneUrl, userFolder);

    // Configure git user
    const userGit = simpleGit(userFolder);
    await userGit.addConfig('user.name', 'saudkhanbpk');
    await userGit.addConfig('user.email', '<EMAIL>');

    // Update specified files
    await updateRepositoryFiles(userFolder, files, repoName);

    // Commit and push changes
    await commitAndPushChanges(userFolder, username, commitMessage || "Update store configuration");

    // Wait for GitHub to process the push
    console.log(`⏳ Waiting for GitHub to process the changes...`);
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Clean up local folder
    console.log(`🧹 Cleaning up local folder...`);
    fse.removeSync(userFolder);

    res.status(200).json({
        message: "✅ Store updated and redeploy triggered!",
        githubUrl: `https://github.com/${GITHUB_USERNAME}/${repoName}`,
        updateMethod: "local-git",
        note: "Netlify will automatically redeploy the site with the new changes"
    });
}

// Debug endpoint to check repositories and template status
export const debugRepositories = async (req, res) => {
    try {
        console.log(`🔍 Debug: Checking repositories and template configuration...`);

        // Verify GitHub token
        const tokenVerification = await verifyGitHubToken();

        // List available repositories
        const availableRepos = await listAvailableRepositories();

        // Check current template repository
        const templateRepoName = TEMPLATE_REPO.split('/').pop().replace('.git', '');
        const templateVerification = await verifyTemplateRepository(templateRepoName);

        // Find potential template repositories
        const potentialTemplates = availableRepos.filter(repo =>
            repo.is_template ||
            repo.name.toLowerCase().includes('template') ||
            repo.name.toLowerCase().includes('store')
        );

        res.status(200).json({
            message: "🔍 Repository debug information",
            tokenValid: tokenVerification.valid,
            currentTemplate: {
                configured: TEMPLATE_REPO,
                name: templateRepoName,
                exists: templateVerification.exists,
                isTemplate: templateVerification.isTemplate
            },
            availableRepositories: availableRepos.length,
            potentialTemplates: potentialTemplates,
            recommendations: potentialTemplates.length > 0
                ? `Consider using one of these repositories as your template: ${potentialTemplates.map(r => r.name).join(', ')}`
                : "No template repositories found. You may need to create one or make an existing repository a template.",
            instructions: {
                createTemplate: "To make a repository a template: Go to repository Settings → Check 'Template repository'",
                updateConfig: "Update TEMPLATE_REPO constant in automation.js to point to the correct repository"
            }
        });

    } catch (err) {
        console.error("❌ Debug failed:", err);
        res.status(500).json({
            message: "Debug failed",
            error: err.message || err.toString()
        });
    }
};

export const health = async (req, res) => {
    try {
        // Check if required environment variables/tokens are available
        const checks = {
            github_token: !!GITHUB_TOKEN,
            netlify_token: !!NETLIFY_AUTH_TOKEN,
            projects_directory: fs.existsSync(PROJECTS_DIR),
            template_repo: !!TEMPLATE_REPO,
            node_env: process.env.NODE_ENV || 'development',
            use_github_api: process.env.USE_GITHUB_API || 'auto'
        };

        const allHealthy = Object.values(checks).slice(0, 4).every(check => check === true);

        res.status(allHealthy ? 200 : 503).json({
            status: allHealthy ? "healthy" : "unhealthy",
            timestamp: new Date().toISOString(),
            checks,
            service: "automation-api"
        });
    } catch (err) {
        console.error("❌ Health check failed:", err);
        res.status(500).json({
            status: "error",
            timestamp: new Date().toISOString(),
            error: err.message,
            service: "automation-api"
        });
    }
};
