import express from 'express'
const router=express.Router();
import {
getReservedTables,
getReservedTablebyId,
postReservedTables,
updateReservedTable,
deleteReservedTable,
deleteReservedbyTableId
} from '../api/tables-reservation&waitingList.js'
router.get('/table-operator',getReservedTables)
router.get('/table-operator/:_id',getReservedTablebyId)
router.post('/table-operator',postReservedTables)
router.put('/table-operator/:_id',updateReservedTable)
router.delete('/table-operator/:_id',deleteReservedTable)
router.delete('/table-operator',deleteReservedbyTableId)
export default router;  