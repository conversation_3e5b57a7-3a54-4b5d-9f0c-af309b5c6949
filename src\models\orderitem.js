import mongoose from "mongoose";

const orderItemSchema = new mongoose.Schema(
  {
    points: { type: Number },
    taxValue: { type: Number },
    dueamount: { type: Number },
    recieveamount: { type: Number },
    Status: { type: String, default: "in progress" },
    OrderNo: { type: Number },
    displayStatus: { type: [] },
    kitchenStatus: { type: String, default: "new order" },
    split: { type: Object, default: {} },
    orderId: { type: mongoose.Schema.Types.ObjectId, ref: "order" },
    table: [{ type: mongoose.Schema.Types.ObjectId, ref: "tables" }],
    ReservedTable: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "tableRservationAndWaitingList",
    },
    orderStatus: {
      type: String,
      enum: ["pos", "drivethru", "dinein", "takeout", "kiosk"],
    },
    discountType: { type: Object },
    couponOffer: { type: Object },
    tax: [{ name: { type: String }, addtax: { type: Number } }],
    taxfake: { type: Number },
    discountAmount: { type: Number },
    couponOfferAmount: { type: Number },
    productWithQty: [
      {
        productId: { type: String },
        qty: { type: Number },
        price: { type: Number },
        discount: { type: Number },
        reason: { type: String },
        oldAmount: { type: Number },
        newAmount: { type: Number },
        discountTypePr: { type: Boolean },
        userDate: { type: String },
        userendTime: { type: String },
        userstartTime: { type: String },
      },
    ],
    product: { type: Array },
    refundData: { type: Array },
    refundTotal: { type: Number, default: 0 },
    selectedModifiers: { type: Array },
    loyalityOffer: { type: Array },
    couponOffer: { type: Array },
    priceExclTax: { type: Number },
    lineValueExclTax: { type: Number },
    lineValueTax: { type: Number },
    lineValue: { type: Number },
    //new
    grandTotal: { type: Number },
    units: { type: Number },
    text: { type: String },
    OrderNumber: { type: String },
    employeeId: { type: mongoose.Schema.Types.ObjectId, ref: "Employee" },
    customerId: { type: mongoose.Schema.Types.ObjectId, ref: "customer" },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: "user",
    },
    paymentType: { type: mongoose.Schema.Types.ObjectId, ref: "paymentlist" },
    customername: { type: String },
    vehicle: { type: String },
    orderNo: { type: String },
    deliveryfee: { type: Number },
    Color: { type: String },
    PaymentStatus: { type: String },
    surCharge: { type: Number },
    tip: {
      amount: Number,
      percentage: Number,
      employeeId: { type: mongoose.Schema.Types.ObjectId, ref: "Employee" },
    },
  },
  { timestamps: true }
);
const orderitem = mongoose.model("orderitem", orderItemSchema);
export default orderitem;
