import mongoose from "mongoose";


const cancelSubSchema = new mongoose.Schema({
    name: {
        type: String
    },
    email: {
        type: String
    },
    reason: {
        type: String
    },
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'user',
        required: true

    }
})
const cancelSubModel = mongoose.model('cancelsub', cancelSubSchema);

export default cancelSubModel;