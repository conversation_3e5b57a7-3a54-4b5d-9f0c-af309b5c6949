import mongoose from 'mongoose';

// User Referral Schema
const userReferralSchema = new mongoose.Schema({
  customerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'PatronPalCustomer',
    required: true,
  },
  referredUser: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  referralCode: {
    type: String,
    unique: true,
    required: true,
  },
  points: {
    type: Boolean,
    default: false,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
}, { timestamps: true });

const UserReferral = mongoose.model('UserReferral', userReferralSchema);

// Customer Referral Schema
const customerReferralSchema = new mongoose.Schema({
  customerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'PatronPalCustomer',
    required: true,
  },
  referredCustomer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'PatronPalCustomer',
  },
  referralCode: {
    type: String,
    unique: true,
    required: true,
  },
  points: {
    type: Boolean,
    default: false,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
}, { timestamps: true });

const CustomerReferral = mongoose.model('CustomerReferral', customerReferralSchema);

export { UserReferral, CustomerReferral };
