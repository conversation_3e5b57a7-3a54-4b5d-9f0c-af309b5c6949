import dotenv from 'dotenv'
import { User } from '../models/User.js'
import Stripe from 'stripe';

dotenv.config();
if (process.env.NODE_ENV === 'production') {
  var stripe = Stripe('***********************************************************************************************************');
} else if (process.env.NODE_ENV === 'development') {
  var stripe = Stripe('***********************************************************************************************************');
  // var stripe = Stripe('sk_test_51MiZTVF1YkHoz4Y5AsHfg9ovHa5zsRFHCfVrHSy5XKvxKtdKSMHpzQ5V0wEfcGHVfoEQ50NjXhCP0aF2aC1Mc05300eCAJlRxu');
}
export const getCompletedAccounts = async (req, res) => {
  try {
    const accounts = await stripe.accounts.list({ limit: 300 });

    // You can process the retrieved accounts as needed
    console.log('Completed Accounts:', accounts.data);

    res.send(accounts.data);
  } catch (error) {
    console.error('Error retrieving completed accounts:', error.message);
    throw error;
  }
}

// Endpoint to fetch all Stripe transactions for a connected account 
export const getAllChargesWithAccountId = async (req, res) => {
  const {
    connectAccountId,
    startDate,
    endDate,
    limit = 30,
    startingAfter,
  } = req.query;

  if (!connectAccountId || !connectAccountId.startsWith("acct_")) {
    return res.status(400).json({ error: "Valid Stripe Connect account ID is required" });
  }

  const params = { limit: Math.min(Number(limit), 100) }; 

  if (startDate && endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return res.status(400).json({ error: "Invalid date format" });
    }
    params.created = {
      gte: Math.floor(start.getTime() / 1000),
      lte: Math.floor(end.getTime() / 1000),
    };
  }

  if (startingAfter) {
    params.starting_after = startingAfter;
  }

  try {
    const result = await stripe.paymentIntents.list(params);

    const relevantStatuses = [
      "succeeded",
      "requires_payment_method",
      "requires_confirmation",
      "canceled",
      "incomplete",
      "failed"
    ];

    const relatedTransactions = result.data.filter((pi) => {
      const relatedToConnect =
        pi.on_behalf_of === connectAccountId ||
        (pi.transfer_data && pi.transfer_data.destination === connectAccountId);
      return relatedToConnect && relevantStatuses.includes(pi.status);
    });

    let totalSuccessAmount = 0;
    let totalFailedAmount = 0;
    let totalSuccessOrders = 0;
    let totalFailedOrders = 0;

    const formattedTransactions = relatedTransactions.map((pi) => {
      const amount = pi.amount / 100;

      if (pi.status === "succeeded") {
        totalSuccessAmount += amount;
        totalSuccessOrders += 1;
      } else {
        totalFailedAmount += amount;
        totalFailedOrders += 1;
      }

      return {
        id: pi.id,
        amount: amount.toFixed(2),
        currency: pi.currency.toUpperCase(),
        status: pi.status,
        created: new Date(pi.created * 1000).toISOString().split("T")[0],
        onBehalfOf: pi.on_behalf_of,
        destination: pi.transfer_data?.destination || null,
      };
    });

    res.json({
      hasMore: result.has_more,
      lastId: result.data.length > 0 ? result.data[result.data.length - 1].id : null,
      summary: {
        totalSuccessOrders,
        totalFailedOrders,
        totalSuccessAmount: totalSuccessAmount.toFixed(2),
        totalFailedAmount: totalFailedAmount.toFixed(2),
      },
      transactions: formattedTransactions,
    });
  } catch (error) {
    console.error("Stripe fetch error:", error.message);
    res.status(500).json({ error: "Internal Server Error", details: error.message });
  }
};


// Create a custom account for a new seller
export const createSellerAccount = async (req, res) => {
  const { userId } = req.query
  console.log('userId: ', userId);
  const userById = await User.findById(userId)
  console.log('userById: ', userById);

  try {
    const account = await stripe.accounts.create({
      type: 'express',
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
    });
    console.log('account: ', account);
    const accountID = account.id;
    console.log('accountID: ', accountID);
    if (userById) {
      const userData = await User.findByIdAndUpdate(userById, { $set: { "stripe_account_id": accountID } })
      console.log(' userData after update : ', userData);
    }
    res.send({ account_id: accountID });
  } catch (error) {
    console.error(err);
    if (error.type === 'StripeInvalidRequestError' && error.raw && error.raw.message) {
      res.status(400).json({ error: error.raw.message });
    } else {
      console.error(error);
      res.status(500).json({ error: 'An error occurred while fetching location' });
    }
  }
};

// Handle seller authorization after they've connected their Stripe account
export const authorizeSeller = async (req, res) => {
  const { code } = req.query;
  const { userId } = req.params;
  console.log('userId: ', userId);
  const userById = await User.findById(userId)
  console.log('userById: ', userById);

  try {
    const response = await stripe.oauth.token({
      grant_type: 'authorization_code',
      code,
    });

    // Save the access token and refresh token to your database
    const { access_token, refresh_token, stripe_user_id } = response;
    if (userById) {
      const userData = await User.findByIdAndUpdate(userById, { $set: { "stripe_account_id": stripe_user_id, "stripe_refresh_token": refresh_token, "stripe_acess_token": access_token } })
      console.log(' userData after update : ', userData);
    }
    res.json({
      access_token,
      refresh_token,
      stripe_user_id,
    });

  } catch (error) {
    console.error(err);
    if (error.type === 'StripeInvalidRequestError' && error.raw && error.raw.message) {
      res.status(400).json({ error: error.raw.message });
    } else {
      console.error(error);
      res.status(500).json({ error: 'An error occurred' });
    }
  }
};
async function deleteStripeAccount(accountId) {
  try {

  } catch (error) {
    console.error(`Error deleting account ${accountId}:`, error.message);
    throw error;
  }
}

export const DeleteConnectAccount = async (req, res) => {
  try {
    const { accountId } = req.params;
    console.log('accountId: ', accountId);

    if (!accountId) {
      return res.status(400).json({ error: 'Account ID is required in the request body.' });
    }

    await stripe.accounts.del(accountId);
    console.log(`Account ${accountId} deleted successfully.`);
    res.status(200).json({ success: true, message: 'Account deleted successfully.' });
  } catch (error) {
    res.status(500).json({ error: 'Internal server error.' });
  }
}

export const getSellerBalance = async (req, res) => {
  const { account_id } = req.params;

  try {
    const balance = await stripe.balance.retrieve({
      stripe_account: account_id,
    });

    // Return the balance information to the client
    res.send(balance);
  } catch (err) {
    console.error(err);
    res.status(500).send({ error: err.message });
  }
};
